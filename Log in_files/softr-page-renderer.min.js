/*! For license information please see softr-page-renderer.min.js.LICENSE.txt */
(()=>{"use strict";var e={483:(e,t,n)=>{n.d(t,{Z:()=>a});var r=n(645),o=n.n(r)()((function(e){return e[1]}));o.push([e.id,"/* IMPORTANT: This file contains global styles to reset some default styles. */\n/* There is exact copy of it in the design-system repo that we connect to the storybook there. */\n/* Ensure these files are in sync */\n/* After we move the design-system to the blocks repo we can use a single file. */\n\n*,\n*::before,\n*::after {\n    box-sizing: inherit;\n}\n\nbody {\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n    -webkit-tap-highlight-color: transparent;\n    box-sizing: border-box;\n}\n\na {\n    text-decoration: none;\n    font-family: inherit;\n    color: inherit;\n    outline: none;\n}\n\nbutton {\n    appearance: none;\n    background-color: transparent;\n    border: none;\n    font-size: inherit;\n    font-family: inherit;\n    line-height: inherit;\n    letter-spacing: inherit;\n    outline: none;\n    color: inherit;\n    padding: 0;\n    margin: 0;\n    text-align: initial;\n}\n\nh1,\nh2,\nh3,\nh4,\nh5,\nh6 {\n    font-size: inherit;\n    font-weight: inherit;\n    margin: 0;\n}\n\nh1,\nh2,\nh3,\nh4,\nh5,\nh6,\np,\nsmall {\n    white-space: pre-wrap;\n}\n\nol,\nul {\n    list-style: none;\n    padding: 0;\n    margin: 0;\n}\n\np {\n    margin: 0;\n}\n\npre {\n    font-family: inherit;\n    margin: 0;\n}\n\n/* unsetting global styles applied by Bootstrap's Reboot reset */\nbutton:focus:not(:focus-visible) {\n    outline: 0;\n}\n\nlabel {\n    margin-bottom: 0;\n}\n\nth {\n    text-align: center;\n}\n\n@media print {\n    @page {\n        size: auto;\n        margin: 0;\n    }\n\n    body {\n        overflow: visible !important;\n    }\n}\n",""]);const a=o},645:e=>{e.exports=function(e){var t=[];return t.toString=function(){return this.map((function(t){var n=e(t);return t[2]?"@media ".concat(t[2]," {").concat(n,"}"):n})).join("")},t.i=function(e,n,r){"string"==typeof e&&(e=[[null,e,""]]);var o={};if(r)for(var a=0;a<this.length;a++){var i=this[a][0];null!=i&&(o[i]=!0)}for(var l=0;l<e.length;l++){var u=[].concat(e[l]);r&&o[u[0]]||(n&&(u[2]?u[2]="".concat(n," and ").concat(u[2]):u[2]=n),t.push(u))}},t}},448:(e,t,n)=>{var r=n(294),o=n(840);function a(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var i=new Set,l={};function u(e,t){s(e,t),s(e+"Capture",t)}function s(e,t){for(l[e]=t,e=0;e<t.length;e++)i.add(t[e])}var c=!("undefined"==typeof window||void 0===window.document||void 0===window.document.createElement),d=Object.prototype.hasOwnProperty,f=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,p={},h={};function m(e,t,n,r,o,a,i){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=a,this.removeEmptyString=i}var v={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach((function(e){v[e]=new m(e,0,!1,e,null,!1,!1)})),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach((function(e){var t=e[0];v[t]=new m(t,1,!1,e[1],null,!1,!1)})),["contentEditable","draggable","spellCheck","value"].forEach((function(e){v[e]=new m(e,2,!1,e.toLowerCase(),null,!1,!1)})),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach((function(e){v[e]=new m(e,2,!1,e,null,!1,!1)})),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach((function(e){v[e]=new m(e,3,!1,e.toLowerCase(),null,!1,!1)})),["checked","multiple","muted","selected"].forEach((function(e){v[e]=new m(e,3,!0,e,null,!1,!1)})),["capture","download"].forEach((function(e){v[e]=new m(e,4,!1,e,null,!1,!1)})),["cols","rows","size","span"].forEach((function(e){v[e]=new m(e,6,!1,e,null,!1,!1)})),["rowSpan","start"].forEach((function(e){v[e]=new m(e,5,!1,e.toLowerCase(),null,!1,!1)}));var g=/[\-:]([a-z])/g;function y(e){return e[1].toUpperCase()}function b(e,t,n,r){var o=v.hasOwnProperty(t)?v[t]:null;(null!==o?0!==o.type:r||!(2<t.length)||"o"!==t[0]&&"O"!==t[0]||"n"!==t[1]&&"N"!==t[1])&&(function(e,t,n,r){if(null==t||function(e,t,n,r){if(null!==n&&0===n.type)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return!r&&(null!==n?!n.acceptsBooleans:"data-"!==(e=e.toLowerCase().slice(0,5))&&"aria-"!==e);default:return!1}}(e,t,n,r))return!0;if(r)return!1;if(null!==n)switch(n.type){case 3:return!t;case 4:return!1===t;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}(t,n,o,r)&&(n=null),r||null===o?function(e){return!!d.call(h,e)||!d.call(p,e)&&(f.test(e)?h[e]=!0:(p[e]=!0,!1))}(t)&&(null===n?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=null===n?3!==o.type&&"":n:(t=o.attributeName,r=o.attributeNamespace,null===n?e.removeAttribute(t):(n=3===(o=o.type)||4===o&&!0===n?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach((function(e){var t=e.replace(g,y);v[t]=new m(t,1,!1,e,null,!1,!1)})),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach((function(e){var t=e.replace(g,y);v[t]=new m(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)})),["xml:base","xml:lang","xml:space"].forEach((function(e){var t=e.replace(g,y);v[t]=new m(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)})),["tabIndex","crossOrigin"].forEach((function(e){v[e]=new m(e,1,!1,e.toLowerCase(),null,!1,!1)})),v.xlinkHref=new m("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach((function(e){v[e]=new m(e,1,!1,e.toLowerCase(),null,!0,!0)}));var w=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,k=Symbol.for("react.element"),x=Symbol.for("react.portal"),S=Symbol.for("react.fragment"),E=Symbol.for("react.strict_mode"),_=Symbol.for("react.profiler"),C=Symbol.for("react.provider"),L=Symbol.for("react.context"),P=Symbol.for("react.forward_ref"),N=Symbol.for("react.suspense"),T=Symbol.for("react.suspense_list"),O=Symbol.for("react.memo"),I=Symbol.for("react.lazy");Symbol.for("react.scope"),Symbol.for("react.debug_trace_mode");var j=Symbol.for("react.offscreen");Symbol.for("react.legacy_hidden"),Symbol.for("react.cache"),Symbol.for("react.tracing_marker");var R=Symbol.iterator;function F(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=R&&e[R]||e["@@iterator"])?e:null}var z,A=Object.assign;function M(e){if(void 0===z)try{throw Error()}catch(e){var t=e.stack.trim().match(/\n( *(at )?)/);z=t&&t[1]||""}return"\n"+z+e}var B=!1;function $(e,t){if(!e||B)return"";B=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),"object"==typeof Reflect&&Reflect.construct){try{Reflect.construct(t,[])}catch(e){var r=e}Reflect.construct(e,[],t)}else{try{t.call()}catch(e){r=e}e.call(t.prototype)}else{try{throw Error()}catch(e){r=e}e()}}catch(t){if(t&&r&&"string"==typeof t.stack){for(var o=t.stack.split("\n"),a=r.stack.split("\n"),i=o.length-1,l=a.length-1;1<=i&&0<=l&&o[i]!==a[l];)l--;for(;1<=i&&0<=l;i--,l--)if(o[i]!==a[l]){if(1!==i||1!==l)do{if(i--,0>--l||o[i]!==a[l]){var u="\n"+o[i].replace(" at new "," at ");return e.displayName&&u.includes("<anonymous>")&&(u=u.replace("<anonymous>",e.displayName)),u}}while(1<=i&&0<=l);break}}}finally{B=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?M(e):""}function D(e){switch(e.tag){case 5:return M(e.type);case 16:return M("Lazy");case 13:return M("Suspense");case 19:return M("SuspenseList");case 0:case 2:case 15:return $(e.type,!1);case 11:return $(e.type.render,!1);case 1:return $(e.type,!0);default:return""}}function U(e){if(null==e)return null;if("function"==typeof e)return e.displayName||e.name||null;if("string"==typeof e)return e;switch(e){case S:return"Fragment";case x:return"Portal";case _:return"Profiler";case E:return"StrictMode";case N:return"Suspense";case T:return"SuspenseList"}if("object"==typeof e)switch(e.$$typeof){case L:return(e.displayName||"Context")+".Consumer";case C:return(e._context.displayName||"Context")+".Provider";case P:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case O:return null!==(t=e.displayName||null)?t:U(e.type)||"Memo";case I:t=e._payload,e=e._init;try{return U(e(t))}catch(e){}}return null}function H(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=(e=t.render).displayName||e.name||"",t.displayName||(""!==e?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return U(t);case 8:return t===E?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if("function"==typeof t)return t.displayName||t.name||null;if("string"==typeof t)return t}return null}function W(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function V(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function Q(e){e._valueTracker||(e._valueTracker=function(e){var t=V(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&void 0!==n&&"function"==typeof n.get&&"function"==typeof n.set){var o=n.get,a=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(e){r=""+e,a.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function q(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=V(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function K(e){if(void 0===(e=e||("undefined"!=typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}function Y(e,t){var n=t.checked;return A({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:null!=n?n:e._wrapperState.initialChecked})}function G(e,t){var n=null==t.defaultValue?"":t.defaultValue,r=null!=t.checked?t.checked:t.defaultChecked;n=W(null!=t.value?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:"checkbox"===t.type||"radio"===t.type?null!=t.checked:null!=t.value}}function J(e,t){null!=(t=t.checked)&&b(e,"checked",t,!1)}function X(e,t){J(e,t);var n=W(t.value),r=t.type;if(null!=n)"number"===r?(0===n&&""===e.value||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if("submit"===r||"reset"===r)return void e.removeAttribute("value");t.hasOwnProperty("value")?ee(e,t.type,n):t.hasOwnProperty("defaultValue")&&ee(e,t.type,W(t.defaultValue)),null==t.checked&&null!=t.defaultChecked&&(e.defaultChecked=!!t.defaultChecked)}function Z(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!("submit"!==r&&"reset"!==r||void 0!==t.value&&null!==t.value))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}""!==(n=e.name)&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,""!==n&&(e.name=n)}function ee(e,t,n){"number"===t&&K(e.ownerDocument)===e||(null==n?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var te=Array.isArray;function ne(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+W(n),t=null,o=0;o<e.length;o++){if(e[o].value===n)return e[o].selected=!0,void(r&&(e[o].defaultSelected=!0));null!==t||e[o].disabled||(t=e[o])}null!==t&&(t.selected=!0)}}function re(e,t){if(null!=t.dangerouslySetInnerHTML)throw Error(a(91));return A({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function oe(e,t){var n=t.value;if(null==n){if(n=t.children,t=t.defaultValue,null!=n){if(null!=t)throw Error(a(92));if(te(n)){if(1<n.length)throw Error(a(93));n=n[0]}t=n}null==t&&(t=""),n=t}e._wrapperState={initialValue:W(n)}}function ae(e,t){var n=W(t.value),r=W(t.defaultValue);null!=n&&((n=""+n)!==e.value&&(e.value=n),null==t.defaultValue&&e.defaultValue!==n&&(e.defaultValue=n)),null!=r&&(e.defaultValue=""+r)}function ie(e){var t=e.textContent;t===e._wrapperState.initialValue&&""!==t&&null!==t&&(e.value=t)}function le(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function ue(e,t){return null==e||"http://www.w3.org/1999/xhtml"===e?le(t):"http://www.w3.org/2000/svg"===e&&"foreignObject"===t?"http://www.w3.org/1999/xhtml":e}var se,ce,de=(ce=function(e,t){if("http://www.w3.org/2000/svg"!==e.namespaceURI||"innerHTML"in e)e.innerHTML=t;else{for((se=se||document.createElement("div")).innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=se.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}},"undefined"!=typeof MSApp&&MSApp.execUnsafeLocalFunction?function(e,t,n,r){MSApp.execUnsafeLocalFunction((function(){return ce(e,t)}))}:ce);function fe(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var pe={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},he=["Webkit","ms","Moz","O"];function me(e,t,n){return null==t||"boolean"==typeof t||""===t?"":n||"number"!=typeof t||0===t||pe.hasOwnProperty(e)&&pe[e]?(""+t).trim():t+"px"}function ve(e,t){for(var n in e=e.style,t)if(t.hasOwnProperty(n)){var r=0===n.indexOf("--"),o=me(n,t[n],r);"float"===n&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}Object.keys(pe).forEach((function(e){he.forEach((function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),pe[t]=pe[e]}))}));var ge=A({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function ye(e,t){if(t){if(ge[e]&&(null!=t.children||null!=t.dangerouslySetInnerHTML))throw Error(a(137,e));if(null!=t.dangerouslySetInnerHTML){if(null!=t.children)throw Error(a(60));if("object"!=typeof t.dangerouslySetInnerHTML||!("__html"in t.dangerouslySetInnerHTML))throw Error(a(61))}if(null!=t.style&&"object"!=typeof t.style)throw Error(a(62))}}function be(e,t){if(-1===e.indexOf("-"))return"string"==typeof t.is;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var we=null;function ke(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var xe=null,Se=null,Ee=null;function _e(e){if(e=wo(e)){if("function"!=typeof xe)throw Error(a(280));var t=e.stateNode;t&&(t=xo(t),xe(e.stateNode,e.type,t))}}function Ce(e){Se?Ee?Ee.push(e):Ee=[e]:Se=e}function Le(){if(Se){var e=Se,t=Ee;if(Ee=Se=null,_e(e),t)for(e=0;e<t.length;e++)_e(t[e])}}function Pe(e,t){return e(t)}function Ne(){}var Te=!1;function Oe(e,t,n){if(Te)return e(t,n);Te=!0;try{return Pe(e,t,n)}finally{Te=!1,(null!==Se||null!==Ee)&&(Ne(),Le())}}function Ie(e,t){var n=e.stateNode;if(null===n)return null;var r=xo(n);if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!=typeof n)throw Error(a(231,t,typeof n));return n}var je=!1;if(c)try{var Re={};Object.defineProperty(Re,"passive",{get:function(){je=!0}}),window.addEventListener("test",Re,Re),window.removeEventListener("test",Re,Re)}catch(ce){je=!1}function Fe(e,t,n,r,o,a,i,l,u){var s=Array.prototype.slice.call(arguments,3);try{t.apply(n,s)}catch(e){this.onError(e)}}var ze=!1,Ae=null,Me=!1,Be=null,$e={onError:function(e){ze=!0,Ae=e}};function De(e,t,n,r,o,a,i,l,u){ze=!1,Ae=null,Fe.apply($e,arguments)}function Ue(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{0!=(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function He(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&null!==(e=e.alternate)&&(t=e.memoizedState),null!==t)return t.dehydrated}return null}function We(e){if(Ue(e)!==e)throw Error(a(188))}function Ve(e){return null!==(e=function(e){var t=e.alternate;if(!t){if(null===(t=Ue(e)))throw Error(a(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(null===o)break;var i=o.alternate;if(null===i){if(null!==(r=o.return)){n=r;continue}break}if(o.child===i.child){for(i=o.child;i;){if(i===n)return We(o),e;if(i===r)return We(o),t;i=i.sibling}throw Error(a(188))}if(n.return!==r.return)n=o,r=i;else{for(var l=!1,u=o.child;u;){if(u===n){l=!0,n=o,r=i;break}if(u===r){l=!0,r=o,n=i;break}u=u.sibling}if(!l){for(u=i.child;u;){if(u===n){l=!0,n=i,r=o;break}if(u===r){l=!0,r=i,n=o;break}u=u.sibling}if(!l)throw Error(a(189))}}if(n.alternate!==r)throw Error(a(190))}if(3!==n.tag)throw Error(a(188));return n.stateNode.current===n?e:t}(e))?Qe(e):null}function Qe(e){if(5===e.tag||6===e.tag)return e;for(e=e.child;null!==e;){var t=Qe(e);if(null!==t)return t;e=e.sibling}return null}var qe=o.unstable_scheduleCallback,Ke=o.unstable_cancelCallback,Ye=o.unstable_shouldYield,Ge=o.unstable_requestPaint,Je=o.unstable_now,Xe=o.unstable_getCurrentPriorityLevel,Ze=o.unstable_ImmediatePriority,et=o.unstable_UserBlockingPriority,tt=o.unstable_NormalPriority,nt=o.unstable_LowPriority,rt=o.unstable_IdlePriority,ot=null,at=null,it=Math.clz32?Math.clz32:function(e){return 0===(e>>>=0)?32:31-(lt(e)/ut|0)|0},lt=Math.log,ut=Math.LN2,st=64,ct=4194304;function dt(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194240&e;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return 130023424&e;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ft(e,t){var n=e.pendingLanes;if(0===n)return 0;var r=0,o=e.suspendedLanes,a=e.pingedLanes,i=268435455&n;if(0!==i){var l=i&~o;0!==l?r=dt(l):0!=(a&=i)&&(r=dt(a))}else 0!=(i=n&~o)?r=dt(i):0!==a&&(r=dt(a));if(0===r)return 0;if(0!==t&&t!==r&&0==(t&o)&&((o=r&-r)>=(a=t&-t)||16===o&&0!=(4194240&a)))return t;if(0!=(4&r)&&(r|=16&n),0!==(t=e.entangledLanes))for(e=e.entanglements,t&=r;0<t;)o=1<<(n=31-it(t)),r|=e[n],t&=~o;return r}function pt(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function ht(e){return 0!=(e=-1073741825&e.pendingLanes)?e:1073741824&e?1073741824:0}function mt(){var e=st;return 0==(4194240&(st<<=1))&&(st=64),e}function vt(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function gt(e,t,n){e.pendingLanes|=t,536870912!==t&&(e.suspendedLanes=0,e.pingedLanes=0),(e=e.eventTimes)[t=31-it(t)]=n}function yt(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-it(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}var bt=0;function wt(e){return 1<(e&=-e)?4<e?0!=(268435455&e)?16:536870912:4:1}var kt,xt,St,Et,_t,Ct=!1,Lt=[],Pt=null,Nt=null,Tt=null,Ot=new Map,It=new Map,jt=[],Rt="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Ft(e,t){switch(e){case"focusin":case"focusout":Pt=null;break;case"dragenter":case"dragleave":Nt=null;break;case"mouseover":case"mouseout":Tt=null;break;case"pointerover":case"pointerout":Ot.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":It.delete(t.pointerId)}}function zt(e,t,n,r,o,a){return null===e||e.nativeEvent!==a?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:a,targetContainers:[o]},null!==t&&null!==(t=wo(t))&&xt(t),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==o&&-1===t.indexOf(o)&&t.push(o),e)}function At(e){var t=bo(e.target);if(null!==t){var n=Ue(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=He(n)))return e.blockedOn=t,void _t(e.priority,(function(){St(n)}))}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function Mt(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=Yt(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(null!==n)return null!==(t=wo(n))&&xt(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);we=r,n.target.dispatchEvent(r),we=null,t.shift()}return!0}function Bt(e,t,n){Mt(e)&&n.delete(t)}function $t(){Ct=!1,null!==Pt&&Mt(Pt)&&(Pt=null),null!==Nt&&Mt(Nt)&&(Nt=null),null!==Tt&&Mt(Tt)&&(Tt=null),Ot.forEach(Bt),It.forEach(Bt)}function Dt(e,t){e.blockedOn===t&&(e.blockedOn=null,Ct||(Ct=!0,o.unstable_scheduleCallback(o.unstable_NormalPriority,$t)))}function Ut(e){function t(t){return Dt(t,e)}if(0<Lt.length){Dt(Lt[0],e);for(var n=1;n<Lt.length;n++){var r=Lt[n];r.blockedOn===e&&(r.blockedOn=null)}}for(null!==Pt&&Dt(Pt,e),null!==Nt&&Dt(Nt,e),null!==Tt&&Dt(Tt,e),Ot.forEach(t),It.forEach(t),n=0;n<jt.length;n++)(r=jt[n]).blockedOn===e&&(r.blockedOn=null);for(;0<jt.length&&null===(n=jt[0]).blockedOn;)At(n),null===n.blockedOn&&jt.shift()}var Ht=w.ReactCurrentBatchConfig,Wt=!0;function Vt(e,t,n,r){var o=bt,a=Ht.transition;Ht.transition=null;try{bt=1,qt(e,t,n,r)}finally{bt=o,Ht.transition=a}}function Qt(e,t,n,r){var o=bt,a=Ht.transition;Ht.transition=null;try{bt=4,qt(e,t,n,r)}finally{bt=o,Ht.transition=a}}function qt(e,t,n,r){if(Wt){var o=Yt(e,t,n,r);if(null===o)Wr(e,t,r,Kt,n),Ft(e,r);else if(function(e,t,n,r,o){switch(t){case"focusin":return Pt=zt(Pt,e,t,n,r,o),!0;case"dragenter":return Nt=zt(Nt,e,t,n,r,o),!0;case"mouseover":return Tt=zt(Tt,e,t,n,r,o),!0;case"pointerover":var a=o.pointerId;return Ot.set(a,zt(Ot.get(a)||null,e,t,n,r,o)),!0;case"gotpointercapture":return a=o.pointerId,It.set(a,zt(It.get(a)||null,e,t,n,r,o)),!0}return!1}(o,e,t,n,r))r.stopPropagation();else if(Ft(e,r),4&t&&-1<Rt.indexOf(e)){for(;null!==o;){var a=wo(o);if(null!==a&&kt(a),null===(a=Yt(e,t,n,r))&&Wr(e,t,r,Kt,n),a===o)break;o=a}null!==o&&r.stopPropagation()}else Wr(e,t,r,null,n)}}var Kt=null;function Yt(e,t,n,r){if(Kt=null,null!==(e=bo(e=ke(r))))if(null===(t=Ue(e)))e=null;else if(13===(n=t.tag)){if(null!==(e=He(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Kt=e,null}function Gt(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Xe()){case Ze:return 1;case et:return 4;case tt:case nt:return 16;case rt:return 536870912;default:return 16}default:return 16}}var Jt=null,Xt=null,Zt=null;function en(){if(Zt)return Zt;var e,t,n=Xt,r=n.length,o="value"in Jt?Jt.value:Jt.textContent,a=o.length;for(e=0;e<r&&n[e]===o[e];e++);var i=r-e;for(t=1;t<=i&&n[r-t]===o[a-t];t++);return Zt=o.slice(e,1<t?1-t:void 0)}function tn(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function nn(){return!0}function rn(){return!1}function on(e){function t(t,n,r,o,a){for(var i in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=o,this.target=a,this.currentTarget=null,e)e.hasOwnProperty(i)&&(t=e[i],this[i]=t?t(o):o[i]);return this.isDefaultPrevented=(null!=o.defaultPrevented?o.defaultPrevented:!1===o.returnValue)?nn:rn,this.isPropagationStopped=rn,this}return A(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!=typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=nn)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!=typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=nn)},persist:function(){},isPersistent:nn}),t}var an,ln,un,sn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},cn=on(sn),dn=A({},sn,{view:0,detail:0}),fn=on(dn),pn=A({},dn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:_n,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==un&&(un&&"mousemove"===e.type?(an=e.screenX-un.screenX,ln=e.screenY-un.screenY):ln=an=0,un=e),an)},movementY:function(e){return"movementY"in e?e.movementY:ln}}),hn=on(pn),mn=on(A({},pn,{dataTransfer:0})),vn=on(A({},dn,{relatedTarget:0})),gn=on(A({},sn,{animationName:0,elapsedTime:0,pseudoElement:0})),yn=A({},sn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),bn=on(yn),wn=on(A({},sn,{data:0})),kn={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},xn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Sn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function En(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=Sn[e])&&!!t[e]}function _n(){return En}var Cn=A({},dn,{key:function(e){if(e.key){var t=kn[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=tn(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?xn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:_n,charCode:function(e){return"keypress"===e.type?tn(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?tn(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}}),Ln=on(Cn),Pn=on(A({},pn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),Nn=on(A({},dn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:_n})),Tn=on(A({},sn,{propertyName:0,elapsedTime:0,pseudoElement:0})),On=A({},pn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),In=on(On),jn=[9,13,27,32],Rn=c&&"CompositionEvent"in window,Fn=null;c&&"documentMode"in document&&(Fn=document.documentMode);var zn=c&&"TextEvent"in window&&!Fn,An=c&&(!Rn||Fn&&8<Fn&&11>=Fn),Mn=String.fromCharCode(32),Bn=!1;function $n(e,t){switch(e){case"keyup":return-1!==jn.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Dn(e){return"object"==typeof(e=e.detail)&&"data"in e?e.data:null}var Un=!1,Hn={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Wn(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!Hn[e.type]:"textarea"===t}function Vn(e,t,n,r){Ce(r),0<(t=Qr(t,"onChange")).length&&(n=new cn("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Qn=null,qn=null;function Kn(e){Mr(e,0)}function Yn(e){if(q(ko(e)))return e}function Gn(e,t){if("change"===e)return t}var Jn=!1;if(c){var Xn;if(c){var Zn="oninput"in document;if(!Zn){var er=document.createElement("div");er.setAttribute("oninput","return;"),Zn="function"==typeof er.oninput}Xn=Zn}else Xn=!1;Jn=Xn&&(!document.documentMode||9<document.documentMode)}function tr(){Qn&&(Qn.detachEvent("onpropertychange",nr),qn=Qn=null)}function nr(e){if("value"===e.propertyName&&Yn(qn)){var t=[];Vn(t,qn,e,ke(e)),Oe(Kn,t)}}function rr(e,t,n){"focusin"===e?(tr(),qn=n,(Qn=t).attachEvent("onpropertychange",nr)):"focusout"===e&&tr()}function or(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return Yn(qn)}function ar(e,t){if("click"===e)return Yn(t)}function ir(e,t){if("input"===e||"change"===e)return Yn(t)}var lr="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t};function ur(e,t){if(lr(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!d.call(t,o)||!lr(e[o],t[o]))return!1}return!0}function sr(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function cr(e,t){var n,r=sr(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=sr(r)}}function dr(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?dr(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function fr(){for(var e=window,t=K();t instanceof e.HTMLIFrameElement;){try{var n="string"==typeof t.contentWindow.location.href}catch(e){n=!1}if(!n)break;t=K((e=t.contentWindow).document)}return t}function pr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}function hr(e){var t=fr(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&dr(n.ownerDocument.documentElement,n)){if(null!==r&&pr(n))if(t=r.start,void 0===(e=r.end)&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if((e=(t=n.ownerDocument||document)&&t.defaultView||window).getSelection){e=e.getSelection();var o=n.textContent.length,a=Math.min(r.start,o);r=void 0===r.end?a:Math.min(r.end,o),!e.extend&&a>r&&(o=r,r=a,a=o),o=cr(n,a);var i=cr(n,r);o&&i&&(1!==e.rangeCount||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==i.node||e.focusOffset!==i.offset)&&((t=t.createRange()).setStart(o.node,o.offset),e.removeAllRanges(),a>r?(e.addRange(t),e.extend(i.node,i.offset)):(t.setEnd(i.node,i.offset),e.addRange(t)))}for(t=[],e=n;e=e.parentNode;)1===e.nodeType&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for("function"==typeof n.focus&&n.focus(),n=0;n<t.length;n++)(e=t[n]).element.scrollLeft=e.left,e.element.scrollTop=e.top}}var mr=c&&"documentMode"in document&&11>=document.documentMode,vr=null,gr=null,yr=null,br=!1;function wr(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;br||null==vr||vr!==K(r)||(r="selectionStart"in(r=vr)&&pr(r)?{start:r.selectionStart,end:r.selectionEnd}:{anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},yr&&ur(yr,r)||(yr=r,0<(r=Qr(gr,"onSelect")).length&&(t=new cn("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=vr)))}function kr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var xr={animationend:kr("Animation","AnimationEnd"),animationiteration:kr("Animation","AnimationIteration"),animationstart:kr("Animation","AnimationStart"),transitionend:kr("Transition","TransitionEnd")},Sr={},Er={};function _r(e){if(Sr[e])return Sr[e];if(!xr[e])return e;var t,n=xr[e];for(t in n)if(n.hasOwnProperty(t)&&t in Er)return Sr[e]=n[t];return e}c&&(Er=document.createElement("div").style,"AnimationEvent"in window||(delete xr.animationend.animation,delete xr.animationiteration.animation,delete xr.animationstart.animation),"TransitionEvent"in window||delete xr.transitionend.transition);var Cr=_r("animationend"),Lr=_r("animationiteration"),Pr=_r("animationstart"),Nr=_r("transitionend"),Tr=new Map,Or="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Ir(e,t){Tr.set(e,t),u(t,[e])}for(var jr=0;jr<Or.length;jr++){var Rr=Or[jr];Ir(Rr.toLowerCase(),"on"+(Rr[0].toUpperCase()+Rr.slice(1)))}Ir(Cr,"onAnimationEnd"),Ir(Lr,"onAnimationIteration"),Ir(Pr,"onAnimationStart"),Ir("dblclick","onDoubleClick"),Ir("focusin","onFocus"),Ir("focusout","onBlur"),Ir(Nr,"onTransitionEnd"),s("onMouseEnter",["mouseout","mouseover"]),s("onMouseLeave",["mouseout","mouseover"]),s("onPointerEnter",["pointerout","pointerover"]),s("onPointerLeave",["pointerout","pointerover"]),u("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),u("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),u("onBeforeInput",["compositionend","keypress","textInput","paste"]),u("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),u("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),u("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Fr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),zr=new Set("cancel close invalid load scroll toggle".split(" ").concat(Fr));function Ar(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,function(e,t,n,r,o,i,l,u,s){if(De.apply(this,arguments),ze){if(!ze)throw Error(a(198));var c=Ae;ze=!1,Ae=null,Me||(Me=!0,Be=c)}}(r,t,void 0,e),e.currentTarget=null}function Mr(e,t){t=0!=(4&t);for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var a=void 0;if(t)for(var i=r.length-1;0<=i;i--){var l=r[i],u=l.instance,s=l.currentTarget;if(l=l.listener,u!==a&&o.isPropagationStopped())break e;Ar(o,l,s),a=u}else for(i=0;i<r.length;i++){if(u=(l=r[i]).instance,s=l.currentTarget,l=l.listener,u!==a&&o.isPropagationStopped())break e;Ar(o,l,s),a=u}}}if(Me)throw e=Be,Me=!1,Be=null,e}function Br(e,t){var n=t[vo];void 0===n&&(n=t[vo]=new Set);var r=e+"__bubble";n.has(r)||(Hr(t,e,2,!1),n.add(r))}function $r(e,t,n){var r=0;t&&(r|=4),Hr(n,e,r,t)}var Dr="_reactListening"+Math.random().toString(36).slice(2);function Ur(e){if(!e[Dr]){e[Dr]=!0,i.forEach((function(t){"selectionchange"!==t&&(zr.has(t)||$r(t,!1,e),$r(t,!0,e))}));var t=9===e.nodeType?e:e.ownerDocument;null===t||t[Dr]||(t[Dr]=!0,$r("selectionchange",!1,t))}}function Hr(e,t,n,r){switch(Gt(t)){case 1:var o=Vt;break;case 4:o=Qt;break;default:o=qt}n=o.bind(null,t,n,e),o=void 0,!je||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(o=!0),r?void 0!==o?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):void 0!==o?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function Wr(e,t,n,r,o){var a=r;if(0==(1&t)&&0==(2&t)&&null!==r)e:for(;;){if(null===r)return;var i=r.tag;if(3===i||4===i){var l=r.stateNode.containerInfo;if(l===o||8===l.nodeType&&l.parentNode===o)break;if(4===i)for(i=r.return;null!==i;){var u=i.tag;if((3===u||4===u)&&((u=i.stateNode.containerInfo)===o||8===u.nodeType&&u.parentNode===o))return;i=i.return}for(;null!==l;){if(null===(i=bo(l)))return;if(5===(u=i.tag)||6===u){r=a=i;continue e}l=l.parentNode}}r=r.return}Oe((function(){var r=a,o=ke(n),i=[];e:{var l=Tr.get(e);if(void 0!==l){var u=cn,s=e;switch(e){case"keypress":if(0===tn(n))break e;case"keydown":case"keyup":u=Ln;break;case"focusin":s="focus",u=vn;break;case"focusout":s="blur",u=vn;break;case"beforeblur":case"afterblur":u=vn;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":u=hn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":u=mn;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":u=Nn;break;case Cr:case Lr:case Pr:u=gn;break;case Nr:u=Tn;break;case"scroll":u=fn;break;case"wheel":u=In;break;case"copy":case"cut":case"paste":u=bn;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":u=Pn}var c=0!=(4&t),d=!c&&"scroll"===e,f=c?null!==l?l+"Capture":null:l;c=[];for(var p,h=r;null!==h;){var m=(p=h).stateNode;if(5===p.tag&&null!==m&&(p=m,null!==f&&null!=(m=Ie(h,f))&&c.push(Vr(h,m,p))),d)break;h=h.return}0<c.length&&(l=new u(l,s,null,n,o),i.push({event:l,listeners:c}))}}if(0==(7&t)){if(u="mouseout"===e||"pointerout"===e,(!(l="mouseover"===e||"pointerover"===e)||n===we||!(s=n.relatedTarget||n.fromElement)||!bo(s)&&!s[mo])&&(u||l)&&(l=o.window===o?o:(l=o.ownerDocument)?l.defaultView||l.parentWindow:window,u?(u=r,null!==(s=(s=n.relatedTarget||n.toElement)?bo(s):null)&&(s!==(d=Ue(s))||5!==s.tag&&6!==s.tag)&&(s=null)):(u=null,s=r),u!==s)){if(c=hn,m="onMouseLeave",f="onMouseEnter",h="mouse","pointerout"!==e&&"pointerover"!==e||(c=Pn,m="onPointerLeave",f="onPointerEnter",h="pointer"),d=null==u?l:ko(u),p=null==s?l:ko(s),(l=new c(m,h+"leave",u,n,o)).target=d,l.relatedTarget=p,m=null,bo(o)===r&&((c=new c(f,h+"enter",s,n,o)).target=p,c.relatedTarget=d,m=c),d=m,u&&s)e:{for(f=s,h=0,p=c=u;p;p=qr(p))h++;for(p=0,m=f;m;m=qr(m))p++;for(;0<h-p;)c=qr(c),h--;for(;0<p-h;)f=qr(f),p--;for(;h--;){if(c===f||null!==f&&c===f.alternate)break e;c=qr(c),f=qr(f)}c=null}else c=null;null!==u&&Kr(i,l,u,c,!1),null!==s&&null!==d&&Kr(i,d,s,c,!0)}if("select"===(u=(l=r?ko(r):window).nodeName&&l.nodeName.toLowerCase())||"input"===u&&"file"===l.type)var v=Gn;else if(Wn(l))if(Jn)v=ir;else{v=or;var g=rr}else(u=l.nodeName)&&"input"===u.toLowerCase()&&("checkbox"===l.type||"radio"===l.type)&&(v=ar);switch(v&&(v=v(e,r))?Vn(i,v,n,o):(g&&g(e,l,r),"focusout"===e&&(g=l._wrapperState)&&g.controlled&&"number"===l.type&&ee(l,"number",l.value)),g=r?ko(r):window,e){case"focusin":(Wn(g)||"true"===g.contentEditable)&&(vr=g,gr=r,yr=null);break;case"focusout":yr=gr=vr=null;break;case"mousedown":br=!0;break;case"contextmenu":case"mouseup":case"dragend":br=!1,wr(i,n,o);break;case"selectionchange":if(mr)break;case"keydown":case"keyup":wr(i,n,o)}var y;if(Rn)e:{switch(e){case"compositionstart":var b="onCompositionStart";break e;case"compositionend":b="onCompositionEnd";break e;case"compositionupdate":b="onCompositionUpdate";break e}b=void 0}else Un?$n(e,n)&&(b="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(b="onCompositionStart");b&&(An&&"ko"!==n.locale&&(Un||"onCompositionStart"!==b?"onCompositionEnd"===b&&Un&&(y=en()):(Xt="value"in(Jt=o)?Jt.value:Jt.textContent,Un=!0)),0<(g=Qr(r,b)).length&&(b=new wn(b,e,null,n,o),i.push({event:b,listeners:g}),(y||null!==(y=Dn(n)))&&(b.data=y))),(y=zn?function(e,t){switch(e){case"compositionend":return Dn(t);case"keypress":return 32!==t.which?null:(Bn=!0,Mn);case"textInput":return(e=t.data)===Mn&&Bn?null:e;default:return null}}(e,n):function(e,t){if(Un)return"compositionend"===e||!Rn&&$n(e,t)?(e=en(),Zt=Xt=Jt=null,Un=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return An&&"ko"!==t.locale?null:t.data}}(e,n))&&0<(r=Qr(r,"onBeforeInput")).length&&(o=new wn("onBeforeInput","beforeinput",null,n,o),i.push({event:o,listeners:r}),o.data=y)}Mr(i,t)}))}function Vr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Qr(e,t){for(var n=t+"Capture",r=[];null!==e;){var o=e,a=o.stateNode;5===o.tag&&null!==a&&(o=a,null!=(a=Ie(e,n))&&r.unshift(Vr(e,a,o)),null!=(a=Ie(e,t))&&r.push(Vr(e,a,o))),e=e.return}return r}function qr(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag);return e||null}function Kr(e,t,n,r,o){for(var a=t._reactName,i=[];null!==n&&n!==r;){var l=n,u=l.alternate,s=l.stateNode;if(null!==u&&u===r)break;5===l.tag&&null!==s&&(l=s,o?null!=(u=Ie(n,a))&&i.unshift(Vr(n,u,l)):o||null!=(u=Ie(n,a))&&i.push(Vr(n,u,l))),n=n.return}0!==i.length&&e.push({event:t,listeners:i})}var Yr=/\r\n?/g,Gr=/\u0000|\uFFFD/g;function Jr(e){return("string"==typeof e?e:""+e).replace(Yr,"\n").replace(Gr,"")}function Xr(e,t,n){if(t=Jr(t),Jr(e)!==t&&n)throw Error(a(425))}function Zr(){}var eo=null,to=null;function no(e,t){return"textarea"===e||"noscript"===e||"string"==typeof t.children||"number"==typeof t.children||"object"==typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var ro="function"==typeof setTimeout?setTimeout:void 0,oo="function"==typeof clearTimeout?clearTimeout:void 0,ao="function"==typeof Promise?Promise:void 0,io="function"==typeof queueMicrotask?queueMicrotask:void 0!==ao?function(e){return ao.resolve(null).then(e).catch(lo)}:ro;function lo(e){setTimeout((function(){throw e}))}function uo(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&8===o.nodeType)if("/$"===(n=o.data)){if(0===r)return e.removeChild(o),void Ut(t);r--}else"$"!==n&&"$?"!==n&&"$!"!==n||r++;n=o}while(n);Ut(t)}function so(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t)break;if("/$"===t)return null}}return e}function co(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}var fo=Math.random().toString(36).slice(2),po="__reactFiber$"+fo,ho="__reactProps$"+fo,mo="__reactContainer$"+fo,vo="__reactEvents$"+fo,go="__reactListeners$"+fo,yo="__reactHandles$"+fo;function bo(e){var t=e[po];if(t)return t;for(var n=e.parentNode;n;){if(t=n[mo]||n[po]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=co(e);null!==e;){if(n=e[po])return n;e=co(e)}return t}n=(e=n).parentNode}return null}function wo(e){return!(e=e[po]||e[mo])||5!==e.tag&&6!==e.tag&&13!==e.tag&&3!==e.tag?null:e}function ko(e){if(5===e.tag||6===e.tag)return e.stateNode;throw Error(a(33))}function xo(e){return e[ho]||null}var So=[],Eo=-1;function _o(e){return{current:e}}function Co(e){0>Eo||(e.current=So[Eo],So[Eo]=null,Eo--)}function Lo(e,t){Eo++,So[Eo]=e.current,e.current=t}var Po={},No=_o(Po),To=_o(!1),Oo=Po;function Io(e,t){var n=e.type.contextTypes;if(!n)return Po;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o,a={};for(o in n)a[o]=t[o];return r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=a),a}function jo(e){return null!=e.childContextTypes}function Ro(){Co(To),Co(No)}function Fo(e,t,n){if(No.current!==Po)throw Error(a(168));Lo(No,t),Lo(To,n)}function zo(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,"function"!=typeof r.getChildContext)return n;for(var o in r=r.getChildContext())if(!(o in t))throw Error(a(108,H(e)||"Unknown",o));return A({},n,r)}function Ao(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Po,Oo=No.current,Lo(No,e),Lo(To,To.current),!0}function Mo(e,t,n){var r=e.stateNode;if(!r)throw Error(a(169));n?(e=zo(e,t,Oo),r.__reactInternalMemoizedMergedChildContext=e,Co(To),Co(No),Lo(No,e)):Co(To),Lo(To,n)}var Bo=null,$o=!1,Do=!1;function Uo(e){null===Bo?Bo=[e]:Bo.push(e)}function Ho(){if(!Do&&null!==Bo){Do=!0;var e=0,t=bt;try{var n=Bo;for(bt=1;e<n.length;e++){var r=n[e];do{r=r(!0)}while(null!==r)}Bo=null,$o=!1}catch(t){throw null!==Bo&&(Bo=Bo.slice(e+1)),qe(Ze,Ho),t}finally{bt=t,Do=!1}}return null}var Wo=[],Vo=0,Qo=null,qo=0,Ko=[],Yo=0,Go=null,Jo=1,Xo="";function Zo(e,t){Wo[Vo++]=qo,Wo[Vo++]=Qo,Qo=e,qo=t}function ea(e,t,n){Ko[Yo++]=Jo,Ko[Yo++]=Xo,Ko[Yo++]=Go,Go=e;var r=Jo;e=Xo;var o=32-it(r)-1;r&=~(1<<o),n+=1;var a=32-it(t)+o;if(30<a){var i=o-o%5;a=(r&(1<<i)-1).toString(32),r>>=i,o-=i,Jo=1<<32-it(t)+o|n<<o|r,Xo=a+e}else Jo=1<<a|n<<o|r,Xo=e}function ta(e){null!==e.return&&(Zo(e,1),ea(e,1,0))}function na(e){for(;e===Qo;)Qo=Wo[--Vo],Wo[Vo]=null,qo=Wo[--Vo],Wo[Vo]=null;for(;e===Go;)Go=Ko[--Yo],Ko[Yo]=null,Xo=Ko[--Yo],Ko[Yo]=null,Jo=Ko[--Yo],Ko[Yo]=null}var ra=null,oa=null,aa=!1,ia=null;function la(e,t){var n=Is(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,null===(t=e.deletions)?(e.deletions=[n],e.flags|=16):t.push(n)}function ua(e,t){switch(e.tag){case 5:var n=e.type;return null!==(t=1!==t.nodeType||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t)&&(e.stateNode=t,ra=e,oa=so(t.firstChild),!0);case 6:return null!==(t=""===e.pendingProps||3!==t.nodeType?null:t)&&(e.stateNode=t,ra=e,oa=null,!0);case 13:return null!==(t=8!==t.nodeType?null:t)&&(n=null!==Go?{id:Jo,overflow:Xo}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},(n=Is(18,null,null,0)).stateNode=t,n.return=e,e.child=n,ra=e,oa=null,!0);default:return!1}}function sa(e){return 0!=(1&e.mode)&&0==(128&e.flags)}function ca(e){if(aa){var t=oa;if(t){var n=t;if(!ua(e,t)){if(sa(e))throw Error(a(418));t=so(n.nextSibling);var r=ra;t&&ua(e,t)?la(r,n):(e.flags=-4097&e.flags|2,aa=!1,ra=e)}}else{if(sa(e))throw Error(a(418));e.flags=-4097&e.flags|2,aa=!1,ra=e}}}function da(e){for(e=e.return;null!==e&&5!==e.tag&&3!==e.tag&&13!==e.tag;)e=e.return;ra=e}function fa(e){if(e!==ra)return!1;if(!aa)return da(e),aa=!0,!1;var t;if((t=3!==e.tag)&&!(t=5!==e.tag)&&(t="head"!==(t=e.type)&&"body"!==t&&!no(e.type,e.memoizedProps)),t&&(t=oa)){if(sa(e))throw pa(),Error(a(418));for(;t;)la(e,t),t=so(t.nextSibling)}if(da(e),13===e.tag){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(a(317));e:{for(e=e.nextSibling,t=0;e;){if(8===e.nodeType){var n=e.data;if("/$"===n){if(0===t){oa=so(e.nextSibling);break e}t--}else"$"!==n&&"$!"!==n&&"$?"!==n||t++}e=e.nextSibling}oa=null}}else oa=ra?so(e.stateNode.nextSibling):null;return!0}function pa(){for(var e=oa;e;)e=so(e.nextSibling)}function ha(){oa=ra=null,aa=!1}function ma(e){null===ia?ia=[e]:ia.push(e)}var va=w.ReactCurrentBatchConfig;function ga(e,t){if(e&&e.defaultProps){for(var n in t=A({},t),e=e.defaultProps)void 0===t[n]&&(t[n]=e[n]);return t}return t}var ya=_o(null),ba=null,wa=null,ka=null;function xa(){ka=wa=ba=null}function Sa(e){var t=ya.current;Co(ya),e._currentValue=t}function Ea(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function _a(e,t){ba=e,ka=wa=null,null!==(e=e.dependencies)&&null!==e.firstContext&&(0!=(e.lanes&t)&&(wl=!0),e.firstContext=null)}function Ca(e){var t=e._currentValue;if(ka!==e)if(e={context:e,memoizedValue:t,next:null},null===wa){if(null===ba)throw Error(a(308));wa=e,ba.dependencies={lanes:0,firstContext:e}}else wa=wa.next=e;return t}var La=null;function Pa(e){null===La?La=[e]:La.push(e)}function Na(e,t,n,r){var o=t.interleaved;return null===o?(n.next=n,Pa(t)):(n.next=o.next,o.next=n),t.interleaved=n,Ta(e,r)}function Ta(e,t){e.lanes|=t;var n=e.alternate;for(null!==n&&(n.lanes|=t),n=e,e=e.return;null!==e;)e.childLanes|=t,null!==(n=e.alternate)&&(n.childLanes|=t),n=e,e=e.return;return 3===n.tag?n.stateNode:null}var Oa=!1;function Ia(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function ja(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Ra(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Fa(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,0!=(2&Nu)){var o=r.pending;return null===o?t.next=t:(t.next=o.next,o.next=t),r.pending=t,Ta(e,n)}return null===(o=r.interleaved)?(t.next=t,Pa(r)):(t.next=o.next,o.next=t),r.interleaved=t,Ta(e,n)}function za(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,0!=(4194240&n))){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,yt(e,n)}}function Aa(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var o=null,a=null;if(null!==(n=n.firstBaseUpdate)){do{var i={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};null===a?o=a=i:a=a.next=i,n=n.next}while(null!==n);null===a?o=a=t:a=a.next=t}else o=a=t;return n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:a,shared:r.shared,effects:r.effects},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Ma(e,t,n,r){var o=e.updateQueue;Oa=!1;var a=o.firstBaseUpdate,i=o.lastBaseUpdate,l=o.shared.pending;if(null!==l){o.shared.pending=null;var u=l,s=u.next;u.next=null,null===i?a=s:i.next=s,i=u;var c=e.alternate;null!==c&&(l=(c=c.updateQueue).lastBaseUpdate)!==i&&(null===l?c.firstBaseUpdate=s:l.next=s,c.lastBaseUpdate=u)}if(null!==a){var d=o.baseState;for(i=0,c=s=u=null,l=a;;){var f=l.lane,p=l.eventTime;if((r&f)===f){null!==c&&(c=c.next={eventTime:p,lane:0,tag:l.tag,payload:l.payload,callback:l.callback,next:null});e:{var h=e,m=l;switch(f=t,p=n,m.tag){case 1:if("function"==typeof(h=m.payload)){d=h.call(p,d,f);break e}d=h;break e;case 3:h.flags=-65537&h.flags|128;case 0:if(null==(f="function"==typeof(h=m.payload)?h.call(p,d,f):h))break e;d=A({},d,f);break e;case 2:Oa=!0}}null!==l.callback&&0!==l.lane&&(e.flags|=64,null===(f=o.effects)?o.effects=[l]:f.push(l))}else p={eventTime:p,lane:f,tag:l.tag,payload:l.payload,callback:l.callback,next:null},null===c?(s=c=p,u=d):c=c.next=p,i|=f;if(null===(l=l.next)){if(null===(l=o.shared.pending))break;l=(f=l).next,f.next=null,o.lastBaseUpdate=f,o.shared.pending=null}}if(null===c&&(u=d),o.baseState=u,o.firstBaseUpdate=s,o.lastBaseUpdate=c,null!==(t=o.shared.interleaved)){o=t;do{i|=o.lane,o=o.next}while(o!==t)}else null===a&&(o.shared.lanes=0);Au|=i,e.lanes=i,e.memoizedState=d}}function Ba(e,t,n){if(e=t.effects,t.effects=null,null!==e)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(null!==o){if(r.callback=null,r=n,"function"!=typeof o)throw Error(a(191,o));o.call(r)}}}var $a=(new r.Component).refs;function Da(e,t,n,r){n=null==(n=n(r,t=e.memoizedState))?t:A({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var Ua={isMounted:function(e){return!!(e=e._reactInternals)&&Ue(e)===e},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=ts(),o=ns(e),a=Ra(r,o);a.payload=t,null!=n&&(a.callback=n),null!==(t=Fa(e,a,o))&&(rs(t,e,o,r),za(t,e,o))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=ts(),o=ns(e),a=Ra(r,o);a.tag=1,a.payload=t,null!=n&&(a.callback=n),null!==(t=Fa(e,a,o))&&(rs(t,e,o,r),za(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=ts(),r=ns(e),o=Ra(n,r);o.tag=2,null!=t&&(o.callback=t),null!==(t=Fa(e,o,r))&&(rs(t,e,r,n),za(t,e,r))}};function Ha(e,t,n,r,o,a,i){return"function"==typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,a,i):!(t.prototype&&t.prototype.isPureReactComponent&&ur(n,r)&&ur(o,a))}function Wa(e,t,n){var r=!1,o=Po,a=t.contextType;return"object"==typeof a&&null!==a?a=Ca(a):(o=jo(t)?Oo:No.current,a=(r=null!=(r=t.contextTypes))?Io(e,o):Po),t=new t(n,a),e.memoizedState=null!==t.state&&void 0!==t.state?t.state:null,t.updater=Ua,e.stateNode=t,t._reactInternals=e,r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=a),t}function Va(e,t,n,r){e=t.state,"function"==typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"==typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Ua.enqueueReplaceState(t,t.state,null)}function Qa(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs=$a,Ia(e);var a=t.contextType;"object"==typeof a&&null!==a?o.context=Ca(a):(a=jo(t)?Oo:No.current,o.context=Io(e,a)),o.state=e.memoizedState,"function"==typeof(a=t.getDerivedStateFromProps)&&(Da(e,t,a,n),o.state=e.memoizedState),"function"==typeof t.getDerivedStateFromProps||"function"==typeof o.getSnapshotBeforeUpdate||"function"!=typeof o.UNSAFE_componentWillMount&&"function"!=typeof o.componentWillMount||(t=o.state,"function"==typeof o.componentWillMount&&o.componentWillMount(),"function"==typeof o.UNSAFE_componentWillMount&&o.UNSAFE_componentWillMount(),t!==o.state&&Ua.enqueueReplaceState(o,o.state,null),Ma(e,n,o,r),o.state=e.memoizedState),"function"==typeof o.componentDidMount&&(e.flags|=4194308)}function qa(e,t,n){if(null!==(e=n.ref)&&"function"!=typeof e&&"object"!=typeof e){if(n._owner){if(n=n._owner){if(1!==n.tag)throw Error(a(309));var r=n.stateNode}if(!r)throw Error(a(147,e));var o=r,i=""+e;return null!==t&&null!==t.ref&&"function"==typeof t.ref&&t.ref._stringRef===i?t.ref:(t=function(e){var t=o.refs;t===$a&&(t=o.refs={}),null===e?delete t[i]:t[i]=e},t._stringRef=i,t)}if("string"!=typeof e)throw Error(a(284));if(!n._owner)throw Error(a(290,e))}return e}function Ka(e,t){throw e=Object.prototype.toString.call(t),Error(a(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Ya(e){return(0,e._init)(e._payload)}function Ga(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e,t){for(e=new Map;null!==t;)null!==t.key?e.set(t.key,t):e.set(t.index,t),t=t.sibling;return e}function o(e,t){return(e=Rs(e,t)).index=0,e.sibling=null,e}function i(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=2,n):r:(t.flags|=2,n):(t.flags|=1048576,n)}function l(t){return e&&null===t.alternate&&(t.flags|=2),t}function u(e,t,n,r){return null===t||6!==t.tag?((t=Ms(n,e.mode,r)).return=e,t):((t=o(t,n)).return=e,t)}function s(e,t,n,r){var a=n.type;return a===S?d(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===a||"object"==typeof a&&null!==a&&a.$$typeof===I&&Ya(a)===t.type)?((r=o(t,n.props)).ref=qa(e,t,n),r.return=e,r):((r=Fs(n.type,n.key,n.props,null,e.mode,r)).ref=qa(e,t,n),r.return=e,r)}function c(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Bs(n,e.mode,r)).return=e,t):((t=o(t,n.children||[])).return=e,t)}function d(e,t,n,r,a){return null===t||7!==t.tag?((t=zs(n,e.mode,r,a)).return=e,t):((t=o(t,n)).return=e,t)}function f(e,t,n){if("string"==typeof t&&""!==t||"number"==typeof t)return(t=Ms(""+t,e.mode,n)).return=e,t;if("object"==typeof t&&null!==t){switch(t.$$typeof){case k:return(n=Fs(t.type,t.key,t.props,null,e.mode,n)).ref=qa(e,null,t),n.return=e,n;case x:return(t=Bs(t,e.mode,n)).return=e,t;case I:return f(e,(0,t._init)(t._payload),n)}if(te(t)||F(t))return(t=zs(t,e.mode,n,null)).return=e,t;Ka(e,t)}return null}function p(e,t,n,r){var o=null!==t?t.key:null;if("string"==typeof n&&""!==n||"number"==typeof n)return null!==o?null:u(e,t,""+n,r);if("object"==typeof n&&null!==n){switch(n.$$typeof){case k:return n.key===o?s(e,t,n,r):null;case x:return n.key===o?c(e,t,n,r):null;case I:return p(e,t,(o=n._init)(n._payload),r)}if(te(n)||F(n))return null!==o?null:d(e,t,n,r,null);Ka(e,n)}return null}function h(e,t,n,r,o){if("string"==typeof r&&""!==r||"number"==typeof r)return u(t,e=e.get(n)||null,""+r,o);if("object"==typeof r&&null!==r){switch(r.$$typeof){case k:return s(t,e=e.get(null===r.key?n:r.key)||null,r,o);case x:return c(t,e=e.get(null===r.key?n:r.key)||null,r,o);case I:return h(e,t,n,(0,r._init)(r._payload),o)}if(te(r)||F(r))return d(t,e=e.get(n)||null,r,o,null);Ka(t,r)}return null}function m(o,a,l,u){for(var s=null,c=null,d=a,m=a=0,v=null;null!==d&&m<l.length;m++){d.index>m?(v=d,d=null):v=d.sibling;var g=p(o,d,l[m],u);if(null===g){null===d&&(d=v);break}e&&d&&null===g.alternate&&t(o,d),a=i(g,a,m),null===c?s=g:c.sibling=g,c=g,d=v}if(m===l.length)return n(o,d),aa&&Zo(o,m),s;if(null===d){for(;m<l.length;m++)null!==(d=f(o,l[m],u))&&(a=i(d,a,m),null===c?s=d:c.sibling=d,c=d);return aa&&Zo(o,m),s}for(d=r(o,d);m<l.length;m++)null!==(v=h(d,o,m,l[m],u))&&(e&&null!==v.alternate&&d.delete(null===v.key?m:v.key),a=i(v,a,m),null===c?s=v:c.sibling=v,c=v);return e&&d.forEach((function(e){return t(o,e)})),aa&&Zo(o,m),s}function v(o,l,u,s){var c=F(u);if("function"!=typeof c)throw Error(a(150));if(null==(u=c.call(u)))throw Error(a(151));for(var d=c=null,m=l,v=l=0,g=null,y=u.next();null!==m&&!y.done;v++,y=u.next()){m.index>v?(g=m,m=null):g=m.sibling;var b=p(o,m,y.value,s);if(null===b){null===m&&(m=g);break}e&&m&&null===b.alternate&&t(o,m),l=i(b,l,v),null===d?c=b:d.sibling=b,d=b,m=g}if(y.done)return n(o,m),aa&&Zo(o,v),c;if(null===m){for(;!y.done;v++,y=u.next())null!==(y=f(o,y.value,s))&&(l=i(y,l,v),null===d?c=y:d.sibling=y,d=y);return aa&&Zo(o,v),c}for(m=r(o,m);!y.done;v++,y=u.next())null!==(y=h(m,o,v,y.value,s))&&(e&&null!==y.alternate&&m.delete(null===y.key?v:y.key),l=i(y,l,v),null===d?c=y:d.sibling=y,d=y);return e&&m.forEach((function(e){return t(o,e)})),aa&&Zo(o,v),c}return function e(r,a,i,u){if("object"==typeof i&&null!==i&&i.type===S&&null===i.key&&(i=i.props.children),"object"==typeof i&&null!==i){switch(i.$$typeof){case k:e:{for(var s=i.key,c=a;null!==c;){if(c.key===s){if((s=i.type)===S){if(7===c.tag){n(r,c.sibling),(a=o(c,i.props.children)).return=r,r=a;break e}}else if(c.elementType===s||"object"==typeof s&&null!==s&&s.$$typeof===I&&Ya(s)===c.type){n(r,c.sibling),(a=o(c,i.props)).ref=qa(r,c,i),a.return=r,r=a;break e}n(r,c);break}t(r,c),c=c.sibling}i.type===S?((a=zs(i.props.children,r.mode,u,i.key)).return=r,r=a):((u=Fs(i.type,i.key,i.props,null,r.mode,u)).ref=qa(r,a,i),u.return=r,r=u)}return l(r);case x:e:{for(c=i.key;null!==a;){if(a.key===c){if(4===a.tag&&a.stateNode.containerInfo===i.containerInfo&&a.stateNode.implementation===i.implementation){n(r,a.sibling),(a=o(a,i.children||[])).return=r,r=a;break e}n(r,a);break}t(r,a),a=a.sibling}(a=Bs(i,r.mode,u)).return=r,r=a}return l(r);case I:return e(r,a,(c=i._init)(i._payload),u)}if(te(i))return m(r,a,i,u);if(F(i))return v(r,a,i,u);Ka(r,i)}return"string"==typeof i&&""!==i||"number"==typeof i?(i=""+i,null!==a&&6===a.tag?(n(r,a.sibling),(a=o(a,i)).return=r,r=a):(n(r,a),(a=Ms(i,r.mode,u)).return=r,r=a),l(r)):n(r,a)}}var Ja=Ga(!0),Xa=Ga(!1),Za={},ei=_o(Za),ti=_o(Za),ni=_o(Za);function ri(e){if(e===Za)throw Error(a(174));return e}function oi(e,t){switch(Lo(ni,t),Lo(ti,e),Lo(ei,Za),e=t.nodeType){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:ue(null,"");break;default:t=ue(t=(e=8===e?t.parentNode:t).namespaceURI||null,e=e.tagName)}Co(ei),Lo(ei,t)}function ai(){Co(ei),Co(ti),Co(ni)}function ii(e){ri(ni.current);var t=ri(ei.current),n=ue(t,e.type);t!==n&&(Lo(ti,e),Lo(ei,n))}function li(e){ti.current===e&&(Co(ei),Co(ti))}var ui=_o(0);function si(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||"$!"===n.data))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!=(128&t.flags))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var ci=[];function di(){for(var e=0;e<ci.length;e++)ci[e]._workInProgressVersionPrimary=null;ci.length=0}var fi=w.ReactCurrentDispatcher,pi=w.ReactCurrentBatchConfig,hi=0,mi=null,vi=null,gi=null,yi=!1,bi=!1,wi=0,ki=0;function xi(){throw Error(a(321))}function Si(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!lr(e[n],t[n]))return!1;return!0}function Ei(e,t,n,r,o,i){if(hi=i,mi=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,fi.current=null===e||null===e.memoizedState?ll:ul,e=n(r,o),bi){i=0;do{if(bi=!1,wi=0,25<=i)throw Error(a(301));i+=1,gi=vi=null,t.updateQueue=null,fi.current=sl,e=n(r,o)}while(bi)}if(fi.current=il,t=null!==vi&&null!==vi.next,hi=0,gi=vi=mi=null,yi=!1,t)throw Error(a(300));return e}function _i(){var e=0!==wi;return wi=0,e}function Ci(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===gi?mi.memoizedState=gi=e:gi=gi.next=e,gi}function Li(){if(null===vi){var e=mi.alternate;e=null!==e?e.memoizedState:null}else e=vi.next;var t=null===gi?mi.memoizedState:gi.next;if(null!==t)gi=t,vi=e;else{if(null===e)throw Error(a(310));e={memoizedState:(vi=e).memoizedState,baseState:vi.baseState,baseQueue:vi.baseQueue,queue:vi.queue,next:null},null===gi?mi.memoizedState=gi=e:gi=gi.next=e}return gi}function Pi(e,t){return"function"==typeof t?t(e):t}function Ni(e){var t=Li(),n=t.queue;if(null===n)throw Error(a(311));n.lastRenderedReducer=e;var r=vi,o=r.baseQueue,i=n.pending;if(null!==i){if(null!==o){var l=o.next;o.next=i.next,i.next=l}r.baseQueue=o=i,n.pending=null}if(null!==o){i=o.next,r=r.baseState;var u=l=null,s=null,c=i;do{var d=c.lane;if((hi&d)===d)null!==s&&(s=s.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),r=c.hasEagerState?c.eagerState:e(r,c.action);else{var f={lane:d,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};null===s?(u=s=f,l=r):s=s.next=f,mi.lanes|=d,Au|=d}c=c.next}while(null!==c&&c!==i);null===s?l=r:s.next=u,lr(r,t.memoizedState)||(wl=!0),t.memoizedState=r,t.baseState=l,t.baseQueue=s,n.lastRenderedState=r}if(null!==(e=n.interleaved)){o=e;do{i=o.lane,mi.lanes|=i,Au|=i,o=o.next}while(o!==e)}else null===o&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Ti(e){var t=Li(),n=t.queue;if(null===n)throw Error(a(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,i=t.memoizedState;if(null!==o){n.pending=null;var l=o=o.next;do{i=e(i,l.action),l=l.next}while(l!==o);lr(i,t.memoizedState)||(wl=!0),t.memoizedState=i,null===t.baseQueue&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function Oi(){}function Ii(e,t){var n=mi,r=Li(),o=t(),i=!lr(r.memoizedState,o);if(i&&(r.memoizedState=o,wl=!0),r=r.queue,Wi(Fi.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||null!==gi&&1&gi.memoizedState.tag){if(n.flags|=2048,Bi(9,Ri.bind(null,n,r,o,t),void 0,null),null===Tu)throw Error(a(349));0!=(30&hi)||ji(n,t,o)}return o}function ji(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=mi.updateQueue)?(t={lastEffect:null,stores:null},mi.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function Ri(e,t,n,r){t.value=n,t.getSnapshot=r,zi(t)&&Ai(e)}function Fi(e,t,n){return n((function(){zi(t)&&Ai(e)}))}function zi(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!lr(e,n)}catch(e){return!0}}function Ai(e){var t=Ta(e,1);null!==t&&rs(t,e,1,-1)}function Mi(e){var t=Ci();return"function"==typeof e&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Pi,lastRenderedState:e},t.queue=e,e=e.dispatch=nl.bind(null,mi,e),[t.memoizedState,e]}function Bi(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},null===(t=mi.updateQueue)?(t={lastEffect:null,stores:null},mi.updateQueue=t,t.lastEffect=e.next=e):null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function $i(){return Li().memoizedState}function Di(e,t,n,r){var o=Ci();mi.flags|=e,o.memoizedState=Bi(1|t,n,void 0,void 0===r?null:r)}function Ui(e,t,n,r){var o=Li();r=void 0===r?null:r;var a=void 0;if(null!==vi){var i=vi.memoizedState;if(a=i.destroy,null!==r&&Si(r,i.deps))return void(o.memoizedState=Bi(t,n,a,r))}mi.flags|=e,o.memoizedState=Bi(1|t,n,a,r)}function Hi(e,t){return Di(8390656,8,e,t)}function Wi(e,t){return Ui(2048,8,e,t)}function Vi(e,t){return Ui(4,2,e,t)}function Qi(e,t){return Ui(4,4,e,t)}function qi(e,t){return"function"==typeof t?(e=e(),t(e),function(){t(null)}):null!=t?(e=e(),t.current=e,function(){t.current=null}):void 0}function Ki(e,t,n){return n=null!=n?n.concat([e]):null,Ui(4,4,qi.bind(null,t,e),n)}function Yi(){}function Gi(e,t){var n=Li();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&Si(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Ji(e,t){var n=Li();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&Si(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Xi(e,t,n){return 0==(21&hi)?(e.baseState&&(e.baseState=!1,wl=!0),e.memoizedState=n):(lr(n,t)||(n=mt(),mi.lanes|=n,Au|=n,e.baseState=!0),t)}function Zi(e,t){var n=bt;bt=0!==n&&4>n?n:4,e(!0);var r=pi.transition;pi.transition={};try{e(!1),t()}finally{bt=n,pi.transition=r}}function el(){return Li().memoizedState}function tl(e,t,n){var r=ns(e);n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},rl(e)?ol(t,n):null!==(n=Na(e,t,n,r))&&(rs(n,e,r,ts()),al(n,t,r))}function nl(e,t,n){var r=ns(e),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(rl(e))ol(t,o);else{var a=e.alternate;if(0===e.lanes&&(null===a||0===a.lanes)&&null!==(a=t.lastRenderedReducer))try{var i=t.lastRenderedState,l=a(i,n);if(o.hasEagerState=!0,o.eagerState=l,lr(l,i)){var u=t.interleaved;return null===u?(o.next=o,Pa(t)):(o.next=u.next,u.next=o),void(t.interleaved=o)}}catch(e){}null!==(n=Na(e,t,o,r))&&(rs(n,e,r,o=ts()),al(n,t,r))}}function rl(e){var t=e.alternate;return e===mi||null!==t&&t===mi}function ol(e,t){bi=yi=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function al(e,t,n){if(0!=(4194240&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,yt(e,n)}}var il={readContext:Ca,useCallback:xi,useContext:xi,useEffect:xi,useImperativeHandle:xi,useInsertionEffect:xi,useLayoutEffect:xi,useMemo:xi,useReducer:xi,useRef:xi,useState:xi,useDebugValue:xi,useDeferredValue:xi,useTransition:xi,useMutableSource:xi,useSyncExternalStore:xi,useId:xi,unstable_isNewReconciler:!1},ll={readContext:Ca,useCallback:function(e,t){return Ci().memoizedState=[e,void 0===t?null:t],e},useContext:Ca,useEffect:Hi,useImperativeHandle:function(e,t,n){return n=null!=n?n.concat([e]):null,Di(4194308,4,qi.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Di(4194308,4,e,t)},useInsertionEffect:function(e,t){return Di(4,2,e,t)},useMemo:function(e,t){var n=Ci();return t=void 0===t?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Ci();return t=void 0!==n?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=tl.bind(null,mi,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},Ci().memoizedState=e},useState:Mi,useDebugValue:Yi,useDeferredValue:function(e){return Ci().memoizedState=e},useTransition:function(){var e=Mi(!1),t=e[0];return e=Zi.bind(null,e[1]),Ci().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=mi,o=Ci();if(aa){if(void 0===n)throw Error(a(407));n=n()}else{if(n=t(),null===Tu)throw Error(a(349));0!=(30&hi)||ji(r,t,n)}o.memoizedState=n;var i={value:n,getSnapshot:t};return o.queue=i,Hi(Fi.bind(null,r,i,e),[e]),r.flags|=2048,Bi(9,Ri.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=Ci(),t=Tu.identifierPrefix;if(aa){var n=Xo;t=":"+t+"R"+(n=(Jo&~(1<<32-it(Jo)-1)).toString(32)+n),0<(n=wi++)&&(t+="H"+n.toString(32)),t+=":"}else t=":"+t+"r"+(n=ki++).toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},ul={readContext:Ca,useCallback:Gi,useContext:Ca,useEffect:Wi,useImperativeHandle:Ki,useInsertionEffect:Vi,useLayoutEffect:Qi,useMemo:Ji,useReducer:Ni,useRef:$i,useState:function(){return Ni(Pi)},useDebugValue:Yi,useDeferredValue:function(e){return Xi(Li(),vi.memoizedState,e)},useTransition:function(){return[Ni(Pi)[0],Li().memoizedState]},useMutableSource:Oi,useSyncExternalStore:Ii,useId:el,unstable_isNewReconciler:!1},sl={readContext:Ca,useCallback:Gi,useContext:Ca,useEffect:Wi,useImperativeHandle:Ki,useInsertionEffect:Vi,useLayoutEffect:Qi,useMemo:Ji,useReducer:Ti,useRef:$i,useState:function(){return Ti(Pi)},useDebugValue:Yi,useDeferredValue:function(e){var t=Li();return null===vi?t.memoizedState=e:Xi(t,vi.memoizedState,e)},useTransition:function(){return[Ti(Pi)[0],Li().memoizedState]},useMutableSource:Oi,useSyncExternalStore:Ii,useId:el,unstable_isNewReconciler:!1};function cl(e,t){try{var n="",r=t;do{n+=D(r),r=r.return}while(r);var o=n}catch(e){o="\nError generating stack: "+e.message+"\n"+e.stack}return{value:e,source:t,stack:o,digest:null}}function dl(e,t,n){return{value:e,source:null,stack:null!=n?n:null,digest:null!=t?t:null}}function fl(e,t){try{console.error(t.value)}catch(e){setTimeout((function(){throw e}))}}var pl="function"==typeof WeakMap?WeakMap:Map;function hl(e,t,n){(n=Ra(-1,n)).tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Vu||(Vu=!0,Qu=r),fl(0,t)},n}function ml(e,t,n){(n=Ra(-1,n)).tag=3;var r=e.type.getDerivedStateFromError;if("function"==typeof r){var o=t.value;n.payload=function(){return r(o)},n.callback=function(){fl(0,t)}}var a=e.stateNode;return null!==a&&"function"==typeof a.componentDidCatch&&(n.callback=function(){fl(0,t),"function"!=typeof r&&(null===qu?qu=new Set([this]):qu.add(this));var e=t.stack;this.componentDidCatch(t.value,{componentStack:null!==e?e:""})}),n}function vl(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new pl;var o=new Set;r.set(t,o)}else void 0===(o=r.get(t))&&(o=new Set,r.set(t,o));o.has(n)||(o.add(n),e=Cs.bind(null,e,t,n),t.then(e,e))}function gl(e){do{var t;if((t=13===e.tag)&&(t=null===(t=e.memoizedState)||null!==t.dehydrated),t)return e;e=e.return}while(null!==e);return null}function yl(e,t,n,r,o){return 0==(1&e.mode)?(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,1===n.tag&&(null===n.alternate?n.tag=17:((t=Ra(-1,1)).tag=2,Fa(n,t,1))),n.lanes|=1),e):(e.flags|=65536,e.lanes=o,e)}var bl=w.ReactCurrentOwner,wl=!1;function kl(e,t,n,r){t.child=null===e?Xa(t,null,n,r):Ja(t,e.child,n,r)}function xl(e,t,n,r,o){n=n.render;var a=t.ref;return _a(t,o),r=Ei(e,t,n,r,a,o),n=_i(),null===e||wl?(aa&&n&&ta(t),t.flags|=1,kl(e,t,r,o),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,Vl(e,t,o))}function Sl(e,t,n,r,o){if(null===e){var a=n.type;return"function"!=typeof a||js(a)||void 0!==a.defaultProps||null!==n.compare||void 0!==n.defaultProps?((e=Fs(n.type,null,r,t,t.mode,o)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=a,El(e,t,a,r,o))}if(a=e.child,0==(e.lanes&o)){var i=a.memoizedProps;if((n=null!==(n=n.compare)?n:ur)(i,r)&&e.ref===t.ref)return Vl(e,t,o)}return t.flags|=1,(e=Rs(a,r)).ref=t.ref,e.return=t,t.child=e}function El(e,t,n,r,o){if(null!==e){var a=e.memoizedProps;if(ur(a,r)&&e.ref===t.ref){if(wl=!1,t.pendingProps=r=a,0==(e.lanes&o))return t.lanes=e.lanes,Vl(e,t,o);0!=(131072&e.flags)&&(wl=!0)}}return Ll(e,t,n,r,o)}function _l(e,t,n){var r=t.pendingProps,o=r.children,a=null!==e?e.memoizedState:null;if("hidden"===r.mode)if(0==(1&t.mode))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Lo(Ru,ju),ju|=n;else{if(0==(1073741824&n))return e=null!==a?a.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Lo(Ru,ju),ju|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=null!==a?a.baseLanes:n,Lo(Ru,ju),ju|=r}else null!==a?(r=a.baseLanes|n,t.memoizedState=null):r=n,Lo(Ru,ju),ju|=r;return kl(e,t,o,n),t.child}function Cl(e,t){var n=t.ref;(null===e&&null!==n||null!==e&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Ll(e,t,n,r,o){var a=jo(n)?Oo:No.current;return a=Io(t,a),_a(t,o),n=Ei(e,t,n,r,a,o),r=_i(),null===e||wl?(aa&&r&&ta(t),t.flags|=1,kl(e,t,n,o),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,Vl(e,t,o))}function Pl(e,t,n,r,o){if(jo(n)){var a=!0;Ao(t)}else a=!1;if(_a(t,o),null===t.stateNode)Wl(e,t),Wa(t,n,r),Qa(t,n,r,o),r=!0;else if(null===e){var i=t.stateNode,l=t.memoizedProps;i.props=l;var u=i.context,s=n.contextType;s="object"==typeof s&&null!==s?Ca(s):Io(t,s=jo(n)?Oo:No.current);var c=n.getDerivedStateFromProps,d="function"==typeof c||"function"==typeof i.getSnapshotBeforeUpdate;d||"function"!=typeof i.UNSAFE_componentWillReceiveProps&&"function"!=typeof i.componentWillReceiveProps||(l!==r||u!==s)&&Va(t,i,r,s),Oa=!1;var f=t.memoizedState;i.state=f,Ma(t,r,i,o),u=t.memoizedState,l!==r||f!==u||To.current||Oa?("function"==typeof c&&(Da(t,n,c,r),u=t.memoizedState),(l=Oa||Ha(t,n,l,r,f,u,s))?(d||"function"!=typeof i.UNSAFE_componentWillMount&&"function"!=typeof i.componentWillMount||("function"==typeof i.componentWillMount&&i.componentWillMount(),"function"==typeof i.UNSAFE_componentWillMount&&i.UNSAFE_componentWillMount()),"function"==typeof i.componentDidMount&&(t.flags|=4194308)):("function"==typeof i.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=u),i.props=r,i.state=u,i.context=s,r=l):("function"==typeof i.componentDidMount&&(t.flags|=4194308),r=!1)}else{i=t.stateNode,ja(e,t),l=t.memoizedProps,s=t.type===t.elementType?l:ga(t.type,l),i.props=s,d=t.pendingProps,f=i.context,u="object"==typeof(u=n.contextType)&&null!==u?Ca(u):Io(t,u=jo(n)?Oo:No.current);var p=n.getDerivedStateFromProps;(c="function"==typeof p||"function"==typeof i.getSnapshotBeforeUpdate)||"function"!=typeof i.UNSAFE_componentWillReceiveProps&&"function"!=typeof i.componentWillReceiveProps||(l!==d||f!==u)&&Va(t,i,r,u),Oa=!1,f=t.memoizedState,i.state=f,Ma(t,r,i,o);var h=t.memoizedState;l!==d||f!==h||To.current||Oa?("function"==typeof p&&(Da(t,n,p,r),h=t.memoizedState),(s=Oa||Ha(t,n,s,r,f,h,u)||!1)?(c||"function"!=typeof i.UNSAFE_componentWillUpdate&&"function"!=typeof i.componentWillUpdate||("function"==typeof i.componentWillUpdate&&i.componentWillUpdate(r,h,u),"function"==typeof i.UNSAFE_componentWillUpdate&&i.UNSAFE_componentWillUpdate(r,h,u)),"function"==typeof i.componentDidUpdate&&(t.flags|=4),"function"==typeof i.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!=typeof i.componentDidUpdate||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!=typeof i.getSnapshotBeforeUpdate||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=h),i.props=r,i.state=h,i.context=u,r=s):("function"!=typeof i.componentDidUpdate||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!=typeof i.getSnapshotBeforeUpdate||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return Nl(e,t,n,r,a,o)}function Nl(e,t,n,r,o,a){Cl(e,t);var i=0!=(128&t.flags);if(!r&&!i)return o&&Mo(t,n,!1),Vl(e,t,a);r=t.stateNode,bl.current=t;var l=i&&"function"!=typeof n.getDerivedStateFromError?null:r.render();return t.flags|=1,null!==e&&i?(t.child=Ja(t,e.child,null,a),t.child=Ja(t,null,l,a)):kl(e,t,l,a),t.memoizedState=r.state,o&&Mo(t,n,!0),t.child}function Tl(e){var t=e.stateNode;t.pendingContext?Fo(0,t.pendingContext,t.pendingContext!==t.context):t.context&&Fo(0,t.context,!1),oi(e,t.containerInfo)}function Ol(e,t,n,r,o){return ha(),ma(o),t.flags|=256,kl(e,t,n,r),t.child}var Il,jl,Rl,Fl,zl={dehydrated:null,treeContext:null,retryLane:0};function Al(e){return{baseLanes:e,cachePool:null,transitions:null}}function Ml(e,t,n){var r,o=t.pendingProps,i=ui.current,l=!1,u=0!=(128&t.flags);if((r=u)||(r=(null===e||null!==e.memoizedState)&&0!=(2&i)),r?(l=!0,t.flags&=-129):null!==e&&null===e.memoizedState||(i|=1),Lo(ui,1&i),null===e)return ca(t),null!==(e=t.memoizedState)&&null!==(e=e.dehydrated)?(0==(1&t.mode)?t.lanes=1:"$!"===e.data?t.lanes=8:t.lanes=1073741824,null):(u=o.children,e=o.fallback,l?(o=t.mode,l=t.child,u={mode:"hidden",children:u},0==(1&o)&&null!==l?(l.childLanes=0,l.pendingProps=u):l=As(u,o,0,null),e=zs(e,o,n,null),l.return=t,e.return=t,l.sibling=e,t.child=l,t.child.memoizedState=Al(n),t.memoizedState=zl,e):Bl(t,u));if(null!==(i=e.memoizedState)&&null!==(r=i.dehydrated))return function(e,t,n,r,o,i,l){if(n)return 256&t.flags?(t.flags&=-257,$l(e,t,l,r=dl(Error(a(422))))):null!==t.memoizedState?(t.child=e.child,t.flags|=128,null):(i=r.fallback,o=t.mode,r=As({mode:"visible",children:r.children},o,0,null),(i=zs(i,o,l,null)).flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,0!=(1&t.mode)&&Ja(t,e.child,null,l),t.child.memoizedState=Al(l),t.memoizedState=zl,i);if(0==(1&t.mode))return $l(e,t,l,null);if("$!"===o.data){if(r=o.nextSibling&&o.nextSibling.dataset)var u=r.dgst;return r=u,$l(e,t,l,r=dl(i=Error(a(419)),r,void 0))}if(u=0!=(l&e.childLanes),wl||u){if(null!==(r=Tu)){switch(l&-l){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}0!==(o=0!=(o&(r.suspendedLanes|l))?0:o)&&o!==i.retryLane&&(i.retryLane=o,Ta(e,o),rs(r,e,o,-1))}return vs(),$l(e,t,l,r=dl(Error(a(421))))}return"$?"===o.data?(t.flags|=128,t.child=e.child,t=Ps.bind(null,e),o._reactRetry=t,null):(e=i.treeContext,oa=so(o.nextSibling),ra=t,aa=!0,ia=null,null!==e&&(Ko[Yo++]=Jo,Ko[Yo++]=Xo,Ko[Yo++]=Go,Jo=e.id,Xo=e.overflow,Go=t),(t=Bl(t,r.children)).flags|=4096,t)}(e,t,u,o,r,i,n);if(l){l=o.fallback,u=t.mode,r=(i=e.child).sibling;var s={mode:"hidden",children:o.children};return 0==(1&u)&&t.child!==i?((o=t.child).childLanes=0,o.pendingProps=s,t.deletions=null):(o=Rs(i,s)).subtreeFlags=14680064&i.subtreeFlags,null!==r?l=Rs(r,l):(l=zs(l,u,n,null)).flags|=2,l.return=t,o.return=t,o.sibling=l,t.child=o,o=l,l=t.child,u=null===(u=e.child.memoizedState)?Al(n):{baseLanes:u.baseLanes|n,cachePool:null,transitions:u.transitions},l.memoizedState=u,l.childLanes=e.childLanes&~n,t.memoizedState=zl,o}return e=(l=e.child).sibling,o=Rs(l,{mode:"visible",children:o.children}),0==(1&t.mode)&&(o.lanes=n),o.return=t,o.sibling=null,null!==e&&(null===(n=t.deletions)?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=o,t.memoizedState=null,o}function Bl(e,t){return(t=As({mode:"visible",children:t},e.mode,0,null)).return=e,e.child=t}function $l(e,t,n,r){return null!==r&&ma(r),Ja(t,e.child,null,n),(e=Bl(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function Dl(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),Ea(e.return,t,n)}function Ul(e,t,n,r,o){var a=e.memoizedState;null===a?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(a.isBackwards=t,a.rendering=null,a.renderingStartTime=0,a.last=r,a.tail=n,a.tailMode=o)}function Hl(e,t,n){var r=t.pendingProps,o=r.revealOrder,a=r.tail;if(kl(e,t,r.children,n),0!=(2&(r=ui.current)))r=1&r|2,t.flags|=128;else{if(null!==e&&0!=(128&e.flags))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Dl(e,n,t);else if(19===e.tag)Dl(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(Lo(ui,r),0==(1&t.mode))t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;null!==n;)null!==(e=n.alternate)&&null===si(e)&&(o=n),n=n.sibling;null===(n=o)?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),Ul(t,!1,o,n,a);break;case"backwards":for(n=null,o=t.child,t.child=null;null!==o;){if(null!==(e=o.alternate)&&null===si(e)){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}Ul(t,!0,n,null,a);break;case"together":Ul(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Wl(e,t){0==(1&t.mode)&&null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Vl(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),Au|=t.lanes,0==(n&t.childLanes))return null;if(null!==e&&t.child!==e.child)throw Error(a(153));if(null!==t.child){for(n=Rs(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Rs(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function Ql(e,t){if(!aa)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function ql(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;null!==o;)n|=o.lanes|o.childLanes,r|=14680064&o.subtreeFlags,r|=14680064&o.flags,o.return=e,o=o.sibling;else for(o=e.child;null!==o;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Kl(e,t,n){var r=t.pendingProps;switch(na(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return ql(t),null;case 1:case 17:return jo(t.type)&&Ro(),ql(t),null;case 3:return r=t.stateNode,ai(),Co(To),Co(No),di(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),null!==e&&null!==e.child||(fa(t)?t.flags|=4:null===e||e.memoizedState.isDehydrated&&0==(256&t.flags)||(t.flags|=1024,null!==ia&&(ls(ia),ia=null))),jl(e,t),ql(t),null;case 5:li(t);var o=ri(ni.current);if(n=t.type,null!==e&&null!=t.stateNode)Rl(e,t,n,r,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(null===t.stateNode)throw Error(a(166));return ql(t),null}if(e=ri(ei.current),fa(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[po]=t,r[ho]=i,e=0!=(1&t.mode),n){case"dialog":Br("cancel",r),Br("close",r);break;case"iframe":case"object":case"embed":Br("load",r);break;case"video":case"audio":for(o=0;o<Fr.length;o++)Br(Fr[o],r);break;case"source":Br("error",r);break;case"img":case"image":case"link":Br("error",r),Br("load",r);break;case"details":Br("toggle",r);break;case"input":G(r,i),Br("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},Br("invalid",r);break;case"textarea":oe(r,i),Br("invalid",r)}for(var u in ye(n,i),o=null,i)if(i.hasOwnProperty(u)){var s=i[u];"children"===u?"string"==typeof s?r.textContent!==s&&(!0!==i.suppressHydrationWarning&&Xr(r.textContent,s,e),o=["children",s]):"number"==typeof s&&r.textContent!==""+s&&(!0!==i.suppressHydrationWarning&&Xr(r.textContent,s,e),o=["children",""+s]):l.hasOwnProperty(u)&&null!=s&&"onScroll"===u&&Br("scroll",r)}switch(n){case"input":Q(r),Z(r,i,!0);break;case"textarea":Q(r),ie(r);break;case"select":case"option":break;default:"function"==typeof i.onClick&&(r.onclick=Zr)}r=o,t.updateQueue=r,null!==r&&(t.flags|=4)}else{u=9===o.nodeType?o:o.ownerDocument,"http://www.w3.org/1999/xhtml"===e&&(e=le(n)),"http://www.w3.org/1999/xhtml"===e?"script"===n?((e=u.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):"string"==typeof r.is?e=u.createElement(n,{is:r.is}):(e=u.createElement(n),"select"===n&&(u=e,r.multiple?u.multiple=!0:r.size&&(u.size=r.size))):e=u.createElementNS(e,n),e[po]=t,e[ho]=r,Il(e,t,!1,!1),t.stateNode=e;e:{switch(u=be(n,r),n){case"dialog":Br("cancel",e),Br("close",e),o=r;break;case"iframe":case"object":case"embed":Br("load",e),o=r;break;case"video":case"audio":for(o=0;o<Fr.length;o++)Br(Fr[o],e);o=r;break;case"source":Br("error",e),o=r;break;case"img":case"image":case"link":Br("error",e),Br("load",e),o=r;break;case"details":Br("toggle",e),o=r;break;case"input":G(e,r),o=Y(e,r),Br("invalid",e);break;case"option":default:o=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=A({},r,{value:void 0}),Br("invalid",e);break;case"textarea":oe(e,r),o=re(e,r),Br("invalid",e)}for(i in ye(n,o),s=o)if(s.hasOwnProperty(i)){var c=s[i];"style"===i?ve(e,c):"dangerouslySetInnerHTML"===i?null!=(c=c?c.__html:void 0)&&de(e,c):"children"===i?"string"==typeof c?("textarea"!==n||""!==c)&&fe(e,c):"number"==typeof c&&fe(e,""+c):"suppressContentEditableWarning"!==i&&"suppressHydrationWarning"!==i&&"autoFocus"!==i&&(l.hasOwnProperty(i)?null!=c&&"onScroll"===i&&Br("scroll",e):null!=c&&b(e,i,c,u))}switch(n){case"input":Q(e),Z(e,r,!1);break;case"textarea":Q(e),ie(e);break;case"option":null!=r.value&&e.setAttribute("value",""+W(r.value));break;case"select":e.multiple=!!r.multiple,null!=(i=r.value)?ne(e,!!r.multiple,i,!1):null!=r.defaultValue&&ne(e,!!r.multiple,r.defaultValue,!0);break;default:"function"==typeof o.onClick&&(e.onclick=Zr)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}null!==t.ref&&(t.flags|=512,t.flags|=2097152)}return ql(t),null;case 6:if(e&&null!=t.stateNode)Fl(e,t,e.memoizedProps,r);else{if("string"!=typeof r&&null===t.stateNode)throw Error(a(166));if(n=ri(ni.current),ri(ei.current),fa(t)){if(r=t.stateNode,n=t.memoizedProps,r[po]=t,(i=r.nodeValue!==n)&&null!==(e=ra))switch(e.tag){case 3:Xr(r.nodeValue,n,0!=(1&e.mode));break;case 5:!0!==e.memoizedProps.suppressHydrationWarning&&Xr(r.nodeValue,n,0!=(1&e.mode))}i&&(t.flags|=4)}else(r=(9===n.nodeType?n:n.ownerDocument).createTextNode(r))[po]=t,t.stateNode=r}return ql(t),null;case 13:if(Co(ui),r=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(aa&&null!==oa&&0!=(1&t.mode)&&0==(128&t.flags))pa(),ha(),t.flags|=98560,i=!1;else if(i=fa(t),null!==r&&null!==r.dehydrated){if(null===e){if(!i)throw Error(a(318));if(!(i=null!==(i=t.memoizedState)?i.dehydrated:null))throw Error(a(317));i[po]=t}else ha(),0==(128&t.flags)&&(t.memoizedState=null),t.flags|=4;ql(t),i=!1}else null!==ia&&(ls(ia),ia=null),i=!0;if(!i)return 65536&t.flags?t:null}return 0!=(128&t.flags)?(t.lanes=n,t):((r=null!==r)!=(null!==e&&null!==e.memoizedState)&&r&&(t.child.flags|=8192,0!=(1&t.mode)&&(null===e||0!=(1&ui.current)?0===Fu&&(Fu=3):vs())),null!==t.updateQueue&&(t.flags|=4),ql(t),null);case 4:return ai(),jl(e,t),null===e&&Ur(t.stateNode.containerInfo),ql(t),null;case 10:return Sa(t.type._context),ql(t),null;case 19:if(Co(ui),null===(i=t.memoizedState))return ql(t),null;if(r=0!=(128&t.flags),null===(u=i.rendering))if(r)Ql(i,!1);else{if(0!==Fu||null!==e&&0!=(128&e.flags))for(e=t.child;null!==e;){if(null!==(u=si(e))){for(t.flags|=128,Ql(i,!1),null!==(r=u.updateQueue)&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;null!==n;)e=r,(i=n).flags&=14680066,null===(u=i.alternate)?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=u.childLanes,i.lanes=u.lanes,i.child=u.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=u.memoizedProps,i.memoizedState=u.memoizedState,i.updateQueue=u.updateQueue,i.type=u.type,e=u.dependencies,i.dependencies=null===e?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return Lo(ui,1&ui.current|2),t.child}e=e.sibling}null!==i.tail&&Je()>Hu&&(t.flags|=128,r=!0,Ql(i,!1),t.lanes=4194304)}else{if(!r)if(null!==(e=si(u))){if(t.flags|=128,r=!0,null!==(n=e.updateQueue)&&(t.updateQueue=n,t.flags|=4),Ql(i,!0),null===i.tail&&"hidden"===i.tailMode&&!u.alternate&&!aa)return ql(t),null}else 2*Je()-i.renderingStartTime>Hu&&1073741824!==n&&(t.flags|=128,r=!0,Ql(i,!1),t.lanes=4194304);i.isBackwards?(u.sibling=t.child,t.child=u):(null!==(n=i.last)?n.sibling=u:t.child=u,i.last=u)}return null!==i.tail?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=Je(),t.sibling=null,n=ui.current,Lo(ui,r?1&n|2:1&n),t):(ql(t),null);case 22:case 23:return fs(),r=null!==t.memoizedState,null!==e&&null!==e.memoizedState!==r&&(t.flags|=8192),r&&0!=(1&t.mode)?0!=(1073741824&ju)&&(ql(t),6&t.subtreeFlags&&(t.flags|=8192)):ql(t),null;case 24:case 25:return null}throw Error(a(156,t.tag))}function Yl(e,t){switch(na(t),t.tag){case 1:return jo(t.type)&&Ro(),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return ai(),Co(To),Co(No),di(),0!=(65536&(e=t.flags))&&0==(128&e)?(t.flags=-65537&e|128,t):null;case 5:return li(t),null;case 13:if(Co(ui),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(a(340));ha()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return Co(ui),null;case 4:return ai(),null;case 10:return Sa(t.type._context),null;case 22:case 23:return fs(),null;default:return null}}Il=function(e,t){for(var n=t.child;null!==n;){if(5===n.tag||6===n.tag)e.appendChild(n.stateNode);else if(4!==n.tag&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===t)break;for(;null===n.sibling;){if(null===n.return||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},jl=function(){},Rl=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,ri(ei.current);var a,i=null;switch(n){case"input":o=Y(e,o),r=Y(e,r),i=[];break;case"select":o=A({},o,{value:void 0}),r=A({},r,{value:void 0}),i=[];break;case"textarea":o=re(e,o),r=re(e,r),i=[];break;default:"function"!=typeof o.onClick&&"function"==typeof r.onClick&&(e.onclick=Zr)}for(c in ye(n,r),n=null,o)if(!r.hasOwnProperty(c)&&o.hasOwnProperty(c)&&null!=o[c])if("style"===c){var u=o[c];for(a in u)u.hasOwnProperty(a)&&(n||(n={}),n[a]="")}else"dangerouslySetInnerHTML"!==c&&"children"!==c&&"suppressContentEditableWarning"!==c&&"suppressHydrationWarning"!==c&&"autoFocus"!==c&&(l.hasOwnProperty(c)?i||(i=[]):(i=i||[]).push(c,null));for(c in r){var s=r[c];if(u=null!=o?o[c]:void 0,r.hasOwnProperty(c)&&s!==u&&(null!=s||null!=u))if("style"===c)if(u){for(a in u)!u.hasOwnProperty(a)||s&&s.hasOwnProperty(a)||(n||(n={}),n[a]="");for(a in s)s.hasOwnProperty(a)&&u[a]!==s[a]&&(n||(n={}),n[a]=s[a])}else n||(i||(i=[]),i.push(c,n)),n=s;else"dangerouslySetInnerHTML"===c?(s=s?s.__html:void 0,u=u?u.__html:void 0,null!=s&&u!==s&&(i=i||[]).push(c,s)):"children"===c?"string"!=typeof s&&"number"!=typeof s||(i=i||[]).push(c,""+s):"suppressContentEditableWarning"!==c&&"suppressHydrationWarning"!==c&&(l.hasOwnProperty(c)?(null!=s&&"onScroll"===c&&Br("scroll",e),i||u===s||(i=[])):(i=i||[]).push(c,s))}n&&(i=i||[]).push("style",n);var c=i;(t.updateQueue=c)&&(t.flags|=4)}},Fl=function(e,t,n,r){n!==r&&(t.flags|=4)};var Gl=!1,Jl=!1,Xl="function"==typeof WeakSet?WeakSet:Set,Zl=null;function eu(e,t){var n=e.ref;if(null!==n)if("function"==typeof n)try{n(null)}catch(n){_s(e,t,n)}else n.current=null}function tu(e,t,n){try{n()}catch(n){_s(e,t,n)}}var nu=!1;function ru(e,t,n){var r=t.updateQueue;if(null!==(r=null!==r?r.lastEffect:null)){var o=r=r.next;do{if((o.tag&e)===e){var a=o.destroy;o.destroy=void 0,void 0!==a&&tu(t,n,a)}o=o.next}while(o!==r)}}function ou(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function au(e){var t=e.ref;if(null!==t){var n=e.stateNode;e.tag,e=n,"function"==typeof t?t(e):t.current=e}}function iu(e){var t=e.alternate;null!==t&&(e.alternate=null,iu(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&null!==(t=e.stateNode)&&(delete t[po],delete t[ho],delete t[vo],delete t[go],delete t[yo]),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function lu(e){return 5===e.tag||3===e.tag||4===e.tag}function uu(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||lu(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function su(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?8===n.nodeType?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(8===n.nodeType?(t=n.parentNode).insertBefore(e,n):(t=n).appendChild(e),null!=(n=n._reactRootContainer)||null!==t.onclick||(t.onclick=Zr));else if(4!==r&&null!==(e=e.child))for(su(e,t,n),e=e.sibling;null!==e;)su(e,t,n),e=e.sibling}function cu(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&null!==(e=e.child))for(cu(e,t,n),e=e.sibling;null!==e;)cu(e,t,n),e=e.sibling}var du=null,fu=!1;function pu(e,t,n){for(n=n.child;null!==n;)hu(e,t,n),n=n.sibling}function hu(e,t,n){if(at&&"function"==typeof at.onCommitFiberUnmount)try{at.onCommitFiberUnmount(ot,n)}catch(e){}switch(n.tag){case 5:Jl||eu(n,t);case 6:var r=du,o=fu;du=null,pu(e,t,n),fu=o,null!==(du=r)&&(fu?(e=du,n=n.stateNode,8===e.nodeType?e.parentNode.removeChild(n):e.removeChild(n)):du.removeChild(n.stateNode));break;case 18:null!==du&&(fu?(e=du,n=n.stateNode,8===e.nodeType?uo(e.parentNode,n):1===e.nodeType&&uo(e,n),Ut(e)):uo(du,n.stateNode));break;case 4:r=du,o=fu,du=n.stateNode.containerInfo,fu=!0,pu(e,t,n),du=r,fu=o;break;case 0:case 11:case 14:case 15:if(!Jl&&null!==(r=n.updateQueue)&&null!==(r=r.lastEffect)){o=r=r.next;do{var a=o,i=a.destroy;a=a.tag,void 0!==i&&(0!=(2&a)||0!=(4&a))&&tu(n,t,i),o=o.next}while(o!==r)}pu(e,t,n);break;case 1:if(!Jl&&(eu(n,t),"function"==typeof(r=n.stateNode).componentWillUnmount))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(e){_s(n,t,e)}pu(e,t,n);break;case 21:pu(e,t,n);break;case 22:1&n.mode?(Jl=(r=Jl)||null!==n.memoizedState,pu(e,t,n),Jl=r):pu(e,t,n);break;default:pu(e,t,n)}}function mu(e){var t=e.updateQueue;if(null!==t){e.updateQueue=null;var n=e.stateNode;null===n&&(n=e.stateNode=new Xl),t.forEach((function(t){var r=Ns.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))}))}}function vu(e,t){var n=t.deletions;if(null!==n)for(var r=0;r<n.length;r++){var o=n[r];try{var i=e,l=t,u=l;e:for(;null!==u;){switch(u.tag){case 5:du=u.stateNode,fu=!1;break e;case 3:case 4:du=u.stateNode.containerInfo,fu=!0;break e}u=u.return}if(null===du)throw Error(a(160));hu(i,l,o),du=null,fu=!1;var s=o.alternate;null!==s&&(s.return=null),o.return=null}catch(e){_s(o,t,e)}}if(12854&t.subtreeFlags)for(t=t.child;null!==t;)gu(t,e),t=t.sibling}function gu(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(vu(t,e),yu(e),4&r){try{ru(3,e,e.return),ou(3,e)}catch(t){_s(e,e.return,t)}try{ru(5,e,e.return)}catch(t){_s(e,e.return,t)}}break;case 1:vu(t,e),yu(e),512&r&&null!==n&&eu(n,n.return);break;case 5:if(vu(t,e),yu(e),512&r&&null!==n&&eu(n,n.return),32&e.flags){var o=e.stateNode;try{fe(o,"")}catch(t){_s(e,e.return,t)}}if(4&r&&null!=(o=e.stateNode)){var i=e.memoizedProps,l=null!==n?n.memoizedProps:i,u=e.type,s=e.updateQueue;if(e.updateQueue=null,null!==s)try{"input"===u&&"radio"===i.type&&null!=i.name&&J(o,i),be(u,l);var c=be(u,i);for(l=0;l<s.length;l+=2){var d=s[l],f=s[l+1];"style"===d?ve(o,f):"dangerouslySetInnerHTML"===d?de(o,f):"children"===d?fe(o,f):b(o,d,f,c)}switch(u){case"input":X(o,i);break;case"textarea":ae(o,i);break;case"select":var p=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!i.multiple;var h=i.value;null!=h?ne(o,!!i.multiple,h,!1):p!==!!i.multiple&&(null!=i.defaultValue?ne(o,!!i.multiple,i.defaultValue,!0):ne(o,!!i.multiple,i.multiple?[]:"",!1))}o[ho]=i}catch(t){_s(e,e.return,t)}}break;case 6:if(vu(t,e),yu(e),4&r){if(null===e.stateNode)throw Error(a(162));o=e.stateNode,i=e.memoizedProps;try{o.nodeValue=i}catch(t){_s(e,e.return,t)}}break;case 3:if(vu(t,e),yu(e),4&r&&null!==n&&n.memoizedState.isDehydrated)try{Ut(t.containerInfo)}catch(t){_s(e,e.return,t)}break;case 4:default:vu(t,e),yu(e);break;case 13:vu(t,e),yu(e),8192&(o=e.child).flags&&(i=null!==o.memoizedState,o.stateNode.isHidden=i,!i||null!==o.alternate&&null!==o.alternate.memoizedState||(Uu=Je())),4&r&&mu(e);break;case 22:if(d=null!==n&&null!==n.memoizedState,1&e.mode?(Jl=(c=Jl)||d,vu(t,e),Jl=c):vu(t,e),yu(e),8192&r){if(c=null!==e.memoizedState,(e.stateNode.isHidden=c)&&!d&&0!=(1&e.mode))for(Zl=e,d=e.child;null!==d;){for(f=Zl=d;null!==Zl;){switch(h=(p=Zl).child,p.tag){case 0:case 11:case 14:case 15:ru(4,p,p.return);break;case 1:eu(p,p.return);var m=p.stateNode;if("function"==typeof m.componentWillUnmount){r=p,n=p.return;try{t=r,m.props=t.memoizedProps,m.state=t.memoizedState,m.componentWillUnmount()}catch(e){_s(r,n,e)}}break;case 5:eu(p,p.return);break;case 22:if(null!==p.memoizedState){xu(f);continue}}null!==h?(h.return=p,Zl=h):xu(f)}d=d.sibling}e:for(d=null,f=e;;){if(5===f.tag){if(null===d){d=f;try{o=f.stateNode,c?"function"==typeof(i=o.style).setProperty?i.setProperty("display","none","important"):i.display="none":(u=f.stateNode,l=null!=(s=f.memoizedProps.style)&&s.hasOwnProperty("display")?s.display:null,u.style.display=me("display",l))}catch(t){_s(e,e.return,t)}}}else if(6===f.tag){if(null===d)try{f.stateNode.nodeValue=c?"":f.memoizedProps}catch(t){_s(e,e.return,t)}}else if((22!==f.tag&&23!==f.tag||null===f.memoizedState||f===e)&&null!==f.child){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;null===f.sibling;){if(null===f.return||f.return===e)break e;d===f&&(d=null),f=f.return}d===f&&(d=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:vu(t,e),yu(e),4&r&&mu(e);case 21:}}function yu(e){var t=e.flags;if(2&t){try{e:{for(var n=e.return;null!==n;){if(lu(n)){var r=n;break e}n=n.return}throw Error(a(160))}switch(r.tag){case 5:var o=r.stateNode;32&r.flags&&(fe(o,""),r.flags&=-33),cu(e,uu(e),o);break;case 3:case 4:var i=r.stateNode.containerInfo;su(e,uu(e),i);break;default:throw Error(a(161))}}catch(t){_s(e,e.return,t)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function bu(e,t,n){Zl=e,wu(e,t,n)}function wu(e,t,n){for(var r=0!=(1&e.mode);null!==Zl;){var o=Zl,a=o.child;if(22===o.tag&&r){var i=null!==o.memoizedState||Gl;if(!i){var l=o.alternate,u=null!==l&&null!==l.memoizedState||Jl;l=Gl;var s=Jl;if(Gl=i,(Jl=u)&&!s)for(Zl=o;null!==Zl;)u=(i=Zl).child,22===i.tag&&null!==i.memoizedState?Su(o):null!==u?(u.return=i,Zl=u):Su(o);for(;null!==a;)Zl=a,wu(a,t,n),a=a.sibling;Zl=o,Gl=l,Jl=s}ku(e)}else 0!=(8772&o.subtreeFlags)&&null!==a?(a.return=o,Zl=a):ku(e)}}function ku(e){for(;null!==Zl;){var t=Zl;if(0!=(8772&t.flags)){var n=t.alternate;try{if(0!=(8772&t.flags))switch(t.tag){case 0:case 11:case 15:Jl||ou(5,t);break;case 1:var r=t.stateNode;if(4&t.flags&&!Jl)if(null===n)r.componentDidMount();else{var o=t.elementType===t.type?n.memoizedProps:ga(t.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;null!==i&&Ba(t,i,r);break;case 3:var l=t.updateQueue;if(null!==l){if(n=null,null!==t.child)switch(t.child.tag){case 5:case 1:n=t.child.stateNode}Ba(t,l,n)}break;case 5:var u=t.stateNode;if(null===n&&4&t.flags){n=u;var s=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":s.autoFocus&&n.focus();break;case"img":s.src&&(n.src=s.src)}}break;case 6:case 4:case 12:case 19:case 17:case 21:case 22:case 23:case 25:break;case 13:if(null===t.memoizedState){var c=t.alternate;if(null!==c){var d=c.memoizedState;if(null!==d){var f=d.dehydrated;null!==f&&Ut(f)}}}break;default:throw Error(a(163))}Jl||512&t.flags&&au(t)}catch(e){_s(t,t.return,e)}}if(t===e){Zl=null;break}if(null!==(n=t.sibling)){n.return=t.return,Zl=n;break}Zl=t.return}}function xu(e){for(;null!==Zl;){var t=Zl;if(t===e){Zl=null;break}var n=t.sibling;if(null!==n){n.return=t.return,Zl=n;break}Zl=t.return}}function Su(e){for(;null!==Zl;){var t=Zl;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{ou(4,t)}catch(e){_s(t,n,e)}break;case 1:var r=t.stateNode;if("function"==typeof r.componentDidMount){var o=t.return;try{r.componentDidMount()}catch(e){_s(t,o,e)}}var a=t.return;try{au(t)}catch(e){_s(t,a,e)}break;case 5:var i=t.return;try{au(t)}catch(e){_s(t,i,e)}}}catch(e){_s(t,t.return,e)}if(t===e){Zl=null;break}var l=t.sibling;if(null!==l){l.return=t.return,Zl=l;break}Zl=t.return}}var Eu,_u=Math.ceil,Cu=w.ReactCurrentDispatcher,Lu=w.ReactCurrentOwner,Pu=w.ReactCurrentBatchConfig,Nu=0,Tu=null,Ou=null,Iu=0,ju=0,Ru=_o(0),Fu=0,zu=null,Au=0,Mu=0,Bu=0,$u=null,Du=null,Uu=0,Hu=1/0,Wu=null,Vu=!1,Qu=null,qu=null,Ku=!1,Yu=null,Gu=0,Ju=0,Xu=null,Zu=-1,es=0;function ts(){return 0!=(6&Nu)?Je():-1!==Zu?Zu:Zu=Je()}function ns(e){return 0==(1&e.mode)?1:0!=(2&Nu)&&0!==Iu?Iu&-Iu:null!==va.transition?(0===es&&(es=mt()),es):0!==(e=bt)?e:e=void 0===(e=window.event)?16:Gt(e.type)}function rs(e,t,n,r){if(50<Ju)throw Ju=0,Xu=null,Error(a(185));gt(e,n,r),0!=(2&Nu)&&e===Tu||(e===Tu&&(0==(2&Nu)&&(Mu|=n),4===Fu&&us(e,Iu)),os(e,r),1===n&&0===Nu&&0==(1&t.mode)&&(Hu=Je()+500,$o&&Ho()))}function os(e,t){var n=e.callbackNode;!function(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,a=e.pendingLanes;0<a;){var i=31-it(a),l=1<<i,u=o[i];-1===u?0!=(l&n)&&0==(l&r)||(o[i]=pt(l,t)):u<=t&&(e.expiredLanes|=l),a&=~l}}(e,t);var r=ft(e,e===Tu?Iu:0);if(0===r)null!==n&&Ke(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(null!=n&&Ke(n),1===t)0===e.tag?function(e){$o=!0,Uo(e)}(ss.bind(null,e)):Uo(ss.bind(null,e)),io((function(){0==(6&Nu)&&Ho()})),n=null;else{switch(wt(r)){case 1:n=Ze;break;case 4:n=et;break;case 16:default:n=tt;break;case 536870912:n=rt}n=Ts(n,as.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function as(e,t){if(Zu=-1,es=0,0!=(6&Nu))throw Error(a(327));var n=e.callbackNode;if(Ss()&&e.callbackNode!==n)return null;var r=ft(e,e===Tu?Iu:0);if(0===r)return null;if(0!=(30&r)||0!=(r&e.expiredLanes)||t)t=gs(e,r);else{t=r;var o=Nu;Nu|=2;var i=ms();for(Tu===e&&Iu===t||(Wu=null,Hu=Je()+500,ps(e,t));;)try{bs();break}catch(t){hs(e,t)}xa(),Cu.current=i,Nu=o,null!==Ou?t=0:(Tu=null,Iu=0,t=Fu)}if(0!==t){if(2===t&&0!==(o=ht(e))&&(r=o,t=is(e,o)),1===t)throw n=zu,ps(e,0),us(e,r),os(e,Je()),n;if(6===t)us(e,r);else{if(o=e.current.alternate,0==(30&r)&&!function(e){for(var t=e;;){if(16384&t.flags){var n=t.updateQueue;if(null!==n&&null!==(n=n.stores))for(var r=0;r<n.length;r++){var o=n[r],a=o.getSnapshot;o=o.value;try{if(!lr(a(),o))return!1}catch(e){return!1}}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}(o)&&(2===(t=gs(e,r))&&0!==(i=ht(e))&&(r=i,t=is(e,i)),1===t))throw n=zu,ps(e,0),us(e,r),os(e,Je()),n;switch(e.finishedWork=o,e.finishedLanes=r,t){case 0:case 1:throw Error(a(345));case 2:case 5:xs(e,Du,Wu);break;case 3:if(us(e,r),(130023424&r)===r&&10<(t=Uu+500-Je())){if(0!==ft(e,0))break;if(((o=e.suspendedLanes)&r)!==r){ts(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=ro(xs.bind(null,e,Du,Wu),t);break}xs(e,Du,Wu);break;case 4:if(us(e,r),(4194240&r)===r)break;for(t=e.eventTimes,o=-1;0<r;){var l=31-it(r);i=1<<l,(l=t[l])>o&&(o=l),r&=~i}if(r=o,10<(r=(120>(r=Je()-r)?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*_u(r/1960))-r)){e.timeoutHandle=ro(xs.bind(null,e,Du,Wu),r);break}xs(e,Du,Wu);break;default:throw Error(a(329))}}}return os(e,Je()),e.callbackNode===n?as.bind(null,e):null}function is(e,t){var n=$u;return e.current.memoizedState.isDehydrated&&(ps(e,t).flags|=256),2!==(e=gs(e,t))&&(t=Du,Du=n,null!==t&&ls(t)),e}function ls(e){null===Du?Du=e:Du.push.apply(Du,e)}function us(e,t){for(t&=~Bu,t&=~Mu,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-it(t),r=1<<n;e[n]=-1,t&=~r}}function ss(e){if(0!=(6&Nu))throw Error(a(327));Ss();var t=ft(e,0);if(0==(1&t))return os(e,Je()),null;var n=gs(e,t);if(0!==e.tag&&2===n){var r=ht(e);0!==r&&(t=r,n=is(e,r))}if(1===n)throw n=zu,ps(e,0),us(e,t),os(e,Je()),n;if(6===n)throw Error(a(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,xs(e,Du,Wu),os(e,Je()),null}function cs(e,t){var n=Nu;Nu|=1;try{return e(t)}finally{0===(Nu=n)&&(Hu=Je()+500,$o&&Ho())}}function ds(e){null!==Yu&&0===Yu.tag&&0==(6&Nu)&&Ss();var t=Nu;Nu|=1;var n=Pu.transition,r=bt;try{if(Pu.transition=null,bt=1,e)return e()}finally{bt=r,Pu.transition=n,0==(6&(Nu=t))&&Ho()}}function fs(){ju=Ru.current,Co(Ru)}function ps(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(-1!==n&&(e.timeoutHandle=-1,oo(n)),null!==Ou)for(n=Ou.return;null!==n;){var r=n;switch(na(r),r.tag){case 1:null!=(r=r.type.childContextTypes)&&Ro();break;case 3:ai(),Co(To),Co(No),di();break;case 5:li(r);break;case 4:ai();break;case 13:case 19:Co(ui);break;case 10:Sa(r.type._context);break;case 22:case 23:fs()}n=n.return}if(Tu=e,Ou=e=Rs(e.current,null),Iu=ju=t,Fu=0,zu=null,Bu=Mu=Au=0,Du=$u=null,null!==La){for(t=0;t<La.length;t++)if(null!==(r=(n=La[t]).interleaved)){n.interleaved=null;var o=r.next,a=n.pending;if(null!==a){var i=a.next;a.next=o,r.next=i}n.pending=r}La=null}return e}function hs(e,t){for(;;){var n=Ou;try{if(xa(),fi.current=il,yi){for(var r=mi.memoizedState;null!==r;){var o=r.queue;null!==o&&(o.pending=null),r=r.next}yi=!1}if(hi=0,gi=vi=mi=null,bi=!1,wi=0,Lu.current=null,null===n||null===n.return){Fu=1,zu=t,Ou=null;break}e:{var i=e,l=n.return,u=n,s=t;if(t=Iu,u.flags|=32768,null!==s&&"object"==typeof s&&"function"==typeof s.then){var c=s,d=u,f=d.tag;if(0==(1&d.mode)&&(0===f||11===f||15===f)){var p=d.alternate;p?(d.updateQueue=p.updateQueue,d.memoizedState=p.memoizedState,d.lanes=p.lanes):(d.updateQueue=null,d.memoizedState=null)}var h=gl(l);if(null!==h){h.flags&=-257,yl(h,l,u,0,t),1&h.mode&&vl(i,c,t),s=c;var m=(t=h).updateQueue;if(null===m){var v=new Set;v.add(s),t.updateQueue=v}else m.add(s);break e}if(0==(1&t)){vl(i,c,t),vs();break e}s=Error(a(426))}else if(aa&&1&u.mode){var g=gl(l);if(null!==g){0==(65536&g.flags)&&(g.flags|=256),yl(g,l,u,0,t),ma(cl(s,u));break e}}i=s=cl(s,u),4!==Fu&&(Fu=2),null===$u?$u=[i]:$u.push(i),i=l;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t,Aa(i,hl(0,s,t));break e;case 1:u=s;var y=i.type,b=i.stateNode;if(0==(128&i.flags)&&("function"==typeof y.getDerivedStateFromError||null!==b&&"function"==typeof b.componentDidCatch&&(null===qu||!qu.has(b)))){i.flags|=65536,t&=-t,i.lanes|=t,Aa(i,ml(i,u,t));break e}}i=i.return}while(null!==i)}ks(n)}catch(e){t=e,Ou===n&&null!==n&&(Ou=n=n.return);continue}break}}function ms(){var e=Cu.current;return Cu.current=il,null===e?il:e}function vs(){0!==Fu&&3!==Fu&&2!==Fu||(Fu=4),null===Tu||0==(268435455&Au)&&0==(268435455&Mu)||us(Tu,Iu)}function gs(e,t){var n=Nu;Nu|=2;var r=ms();for(Tu===e&&Iu===t||(Wu=null,ps(e,t));;)try{ys();break}catch(t){hs(e,t)}if(xa(),Nu=n,Cu.current=r,null!==Ou)throw Error(a(261));return Tu=null,Iu=0,Fu}function ys(){for(;null!==Ou;)ws(Ou)}function bs(){for(;null!==Ou&&!Ye();)ws(Ou)}function ws(e){var t=Eu(e.alternate,e,ju);e.memoizedProps=e.pendingProps,null===t?ks(e):Ou=t,Lu.current=null}function ks(e){var t=e;do{var n=t.alternate;if(e=t.return,0==(32768&t.flags)){if(null!==(n=Kl(n,t,ju)))return void(Ou=n)}else{if(null!==(n=Yl(n,t)))return n.flags&=32767,void(Ou=n);if(null===e)return Fu=6,void(Ou=null);e.flags|=32768,e.subtreeFlags=0,e.deletions=null}if(null!==(t=t.sibling))return void(Ou=t);Ou=t=e}while(null!==t);0===Fu&&(Fu=5)}function xs(e,t,n){var r=bt,o=Pu.transition;try{Pu.transition=null,bt=1,function(e,t,n,r){do{Ss()}while(null!==Yu);if(0!=(6&Nu))throw Error(a(327));n=e.finishedWork;var o=e.finishedLanes;if(null===n)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(a(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(function(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var o=31-it(n),a=1<<o;t[o]=0,r[o]=-1,e[o]=-1,n&=~a}}(e,i),e===Tu&&(Ou=Tu=null,Iu=0),0==(2064&n.subtreeFlags)&&0==(2064&n.flags)||Ku||(Ku=!0,Ts(tt,(function(){return Ss(),null}))),i=0!=(15990&n.flags),0!=(15990&n.subtreeFlags)||i){i=Pu.transition,Pu.transition=null;var l=bt;bt=1;var u=Nu;Nu|=4,Lu.current=null,function(e,t){if(eo=Wt,pr(e=fr())){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(r&&0!==r.rangeCount){n=r.anchorNode;var o=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch(e){n=null;break e}var l=0,u=-1,s=-1,c=0,d=0,f=e,p=null;t:for(;;){for(var h;f!==n||0!==o&&3!==f.nodeType||(u=l+o),f!==i||0!==r&&3!==f.nodeType||(s=l+r),3===f.nodeType&&(l+=f.nodeValue.length),null!==(h=f.firstChild);)p=f,f=h;for(;;){if(f===e)break t;if(p===n&&++c===o&&(u=l),p===i&&++d===r&&(s=l),null!==(h=f.nextSibling))break;p=(f=p).parentNode}f=h}n=-1===u||-1===s?null:{start:u,end:s}}else n=null}n=n||{start:0,end:0}}else n=null;for(to={focusedElem:e,selectionRange:n},Wt=!1,Zl=t;null!==Zl;)if(e=(t=Zl).child,0!=(1028&t.subtreeFlags)&&null!==e)e.return=t,Zl=e;else for(;null!==Zl;){t=Zl;try{var m=t.alternate;if(0!=(1024&t.flags))switch(t.tag){case 0:case 11:case 15:case 5:case 6:case 4:case 17:break;case 1:if(null!==m){var v=m.memoizedProps,g=m.memoizedState,y=t.stateNode,b=y.getSnapshotBeforeUpdate(t.elementType===t.type?v:ga(t.type,v),g);y.__reactInternalSnapshotBeforeUpdate=b}break;case 3:var w=t.stateNode.containerInfo;1===w.nodeType?w.textContent="":9===w.nodeType&&w.documentElement&&w.removeChild(w.documentElement);break;default:throw Error(a(163))}}catch(e){_s(t,t.return,e)}if(null!==(e=t.sibling)){e.return=t.return,Zl=e;break}Zl=t.return}m=nu,nu=!1}(e,n),gu(n,e),hr(to),Wt=!!eo,to=eo=null,e.current=n,bu(n,e,o),Ge(),Nu=u,bt=l,Pu.transition=i}else e.current=n;if(Ku&&(Ku=!1,Yu=e,Gu=o),0===(i=e.pendingLanes)&&(qu=null),function(e){if(at&&"function"==typeof at.onCommitFiberRoot)try{at.onCommitFiberRoot(ot,e,void 0,128==(128&e.current.flags))}catch(e){}}(n.stateNode),os(e,Je()),null!==t)for(r=e.onRecoverableError,n=0;n<t.length;n++)r((o=t[n]).value,{componentStack:o.stack,digest:o.digest});if(Vu)throw Vu=!1,e=Qu,Qu=null,e;0!=(1&Gu)&&0!==e.tag&&Ss(),0!=(1&(i=e.pendingLanes))?e===Xu?Ju++:(Ju=0,Xu=e):Ju=0,Ho()}(e,t,n,r)}finally{Pu.transition=o,bt=r}return null}function Ss(){if(null!==Yu){var e=wt(Gu),t=Pu.transition,n=bt;try{if(Pu.transition=null,bt=16>e?16:e,null===Yu)var r=!1;else{if(e=Yu,Yu=null,Gu=0,0!=(6&Nu))throw Error(a(331));var o=Nu;for(Nu|=4,Zl=e.current;null!==Zl;){var i=Zl,l=i.child;if(0!=(16&Zl.flags)){var u=i.deletions;if(null!==u){for(var s=0;s<u.length;s++){var c=u[s];for(Zl=c;null!==Zl;){var d=Zl;switch(d.tag){case 0:case 11:case 15:ru(8,d,i)}var f=d.child;if(null!==f)f.return=d,Zl=f;else for(;null!==Zl;){var p=(d=Zl).sibling,h=d.return;if(iu(d),d===c){Zl=null;break}if(null!==p){p.return=h,Zl=p;break}Zl=h}}}var m=i.alternate;if(null!==m){var v=m.child;if(null!==v){m.child=null;do{var g=v.sibling;v.sibling=null,v=g}while(null!==v)}}Zl=i}}if(0!=(2064&i.subtreeFlags)&&null!==l)l.return=i,Zl=l;else e:for(;null!==Zl;){if(0!=(2048&(i=Zl).flags))switch(i.tag){case 0:case 11:case 15:ru(9,i,i.return)}var y=i.sibling;if(null!==y){y.return=i.return,Zl=y;break e}Zl=i.return}}var b=e.current;for(Zl=b;null!==Zl;){var w=(l=Zl).child;if(0!=(2064&l.subtreeFlags)&&null!==w)w.return=l,Zl=w;else e:for(l=b;null!==Zl;){if(0!=(2048&(u=Zl).flags))try{switch(u.tag){case 0:case 11:case 15:ou(9,u)}}catch(e){_s(u,u.return,e)}if(u===l){Zl=null;break e}var k=u.sibling;if(null!==k){k.return=u.return,Zl=k;break e}Zl=u.return}}if(Nu=o,Ho(),at&&"function"==typeof at.onPostCommitFiberRoot)try{at.onPostCommitFiberRoot(ot,e)}catch(e){}r=!0}return r}finally{bt=n,Pu.transition=t}}return!1}function Es(e,t,n){e=Fa(e,t=hl(0,t=cl(n,t),1),1),t=ts(),null!==e&&(gt(e,1,t),os(e,t))}function _s(e,t,n){if(3===e.tag)Es(e,e,n);else for(;null!==t;){if(3===t.tag){Es(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"==typeof t.type.getDerivedStateFromError||"function"==typeof r.componentDidCatch&&(null===qu||!qu.has(r))){t=Fa(t,e=ml(t,e=cl(n,e),1),1),e=ts(),null!==t&&(gt(t,1,e),os(t,e));break}}t=t.return}}function Cs(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),t=ts(),e.pingedLanes|=e.suspendedLanes&n,Tu===e&&(Iu&n)===n&&(4===Fu||3===Fu&&(130023424&Iu)===Iu&&500>Je()-Uu?ps(e,0):Bu|=n),os(e,t)}function Ls(e,t){0===t&&(0==(1&e.mode)?t=1:(t=ct,0==(130023424&(ct<<=1))&&(ct=4194304)));var n=ts();null!==(e=Ta(e,t))&&(gt(e,t,n),os(e,n))}function Ps(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),Ls(e,n)}function Ns(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;null!==o&&(n=o.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(a(314))}null!==r&&r.delete(t),Ls(e,n)}function Ts(e,t){return qe(e,t)}function Os(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Is(e,t,n,r){return new Os(e,t,n,r)}function js(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Rs(e,t){var n=e.alternate;return null===n?((n=Is(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=14680064&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Fs(e,t,n,r,o,i){var l=2;if(r=e,"function"==typeof e)js(e)&&(l=1);else if("string"==typeof e)l=5;else e:switch(e){case S:return zs(n.children,o,i,t);case E:l=8,o|=8;break;case _:return(e=Is(12,n,t,2|o)).elementType=_,e.lanes=i,e;case N:return(e=Is(13,n,t,o)).elementType=N,e.lanes=i,e;case T:return(e=Is(19,n,t,o)).elementType=T,e.lanes=i,e;case j:return As(n,o,i,t);default:if("object"==typeof e&&null!==e)switch(e.$$typeof){case C:l=10;break e;case L:l=9;break e;case P:l=11;break e;case O:l=14;break e;case I:l=16,r=null;break e}throw Error(a(130,null==e?e:typeof e,""))}return(t=Is(l,n,t,o)).elementType=e,t.type=r,t.lanes=i,t}function zs(e,t,n,r){return(e=Is(7,e,r,t)).lanes=n,e}function As(e,t,n,r){return(e=Is(22,e,r,t)).elementType=j,e.lanes=n,e.stateNode={isHidden:!1},e}function Ms(e,t,n){return(e=Is(6,e,null,t)).lanes=n,e}function Bs(e,t,n){return(t=Is(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function $s(e,t,n,r,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=vt(0),this.expirationTimes=vt(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=vt(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function Ds(e,t,n,r,o,a,i,l,u){return e=new $s(e,t,n,l,u),1===t?(t=1,!0===a&&(t|=8)):t=0,a=Is(3,null,null,t),e.current=a,a.stateNode=e,a.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Ia(a),e}function Us(e){if(!e)return Po;e:{if(Ue(e=e._reactInternals)!==e||1!==e.tag)throw Error(a(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(jo(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(null!==t);throw Error(a(171))}if(1===e.tag){var n=e.type;if(jo(n))return zo(e,n,t)}return t}function Hs(e,t,n,r,o,a,i,l,u){return(e=Ds(n,r,!0,e,0,a,0,l,u)).context=Us(null),n=e.current,(a=Ra(r=ts(),o=ns(n))).callback=null!=t?t:null,Fa(n,a,o),e.current.lanes=o,gt(e,o,r),os(e,r),e}function Ws(e,t,n,r){var o=t.current,a=ts(),i=ns(o);return n=Us(n),null===t.context?t.context=n:t.pendingContext=n,(t=Ra(a,i)).payload={element:e},null!==(r=void 0===r?null:r)&&(t.callback=r),null!==(e=Fa(o,t,i))&&(rs(e,o,i,a),za(e,o,i)),i}function Vs(e){return(e=e.current).child?(e.child.tag,e.child.stateNode):null}function Qs(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function qs(e,t){Qs(e,t),(e=e.alternate)&&Qs(e,t)}Eu=function(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps||To.current)wl=!0;else{if(0==(e.lanes&n)&&0==(128&t.flags))return wl=!1,function(e,t,n){switch(t.tag){case 3:Tl(t),ha();break;case 5:ii(t);break;case 1:jo(t.type)&&Ao(t);break;case 4:oi(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,o=t.memoizedProps.value;Lo(ya,r._currentValue),r._currentValue=o;break;case 13:if(null!==(r=t.memoizedState))return null!==r.dehydrated?(Lo(ui,1&ui.current),t.flags|=128,null):0!=(n&t.child.childLanes)?Ml(e,t,n):(Lo(ui,1&ui.current),null!==(e=Vl(e,t,n))?e.sibling:null);Lo(ui,1&ui.current);break;case 19:if(r=0!=(n&t.childLanes),0!=(128&e.flags)){if(r)return Hl(e,t,n);t.flags|=128}if(null!==(o=t.memoizedState)&&(o.rendering=null,o.tail=null,o.lastEffect=null),Lo(ui,ui.current),r)break;return null;case 22:case 23:return t.lanes=0,_l(e,t,n)}return Vl(e,t,n)}(e,t,n);wl=0!=(131072&e.flags)}else wl=!1,aa&&0!=(1048576&t.flags)&&ea(t,qo,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Wl(e,t),e=t.pendingProps;var o=Io(t,No.current);_a(t,n),o=Ei(null,t,r,e,o,n);var i=_i();return t.flags|=1,"object"==typeof o&&null!==o&&"function"==typeof o.render&&void 0===o.$$typeof?(t.tag=1,t.memoizedState=null,t.updateQueue=null,jo(r)?(i=!0,Ao(t)):i=!1,t.memoizedState=null!==o.state&&void 0!==o.state?o.state:null,Ia(t),o.updater=Ua,t.stateNode=o,o._reactInternals=t,Qa(t,r,e,n),t=Nl(null,t,r,!0,i,n)):(t.tag=0,aa&&i&&ta(t),kl(null,t,o,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Wl(e,t),e=t.pendingProps,r=(o=r._init)(r._payload),t.type=r,o=t.tag=function(e){if("function"==typeof e)return js(e)?1:0;if(null!=e){if((e=e.$$typeof)===P)return 11;if(e===O)return 14}return 2}(r),e=ga(r,e),o){case 0:t=Ll(null,t,r,e,n);break e;case 1:t=Pl(null,t,r,e,n);break e;case 11:t=xl(null,t,r,e,n);break e;case 14:t=Sl(null,t,r,ga(r.type,e),n);break e}throw Error(a(306,r,""))}return t;case 0:return r=t.type,o=t.pendingProps,Ll(e,t,r,o=t.elementType===r?o:ga(r,o),n);case 1:return r=t.type,o=t.pendingProps,Pl(e,t,r,o=t.elementType===r?o:ga(r,o),n);case 3:e:{if(Tl(t),null===e)throw Error(a(387));r=t.pendingProps,o=(i=t.memoizedState).element,ja(e,t),Ma(t,r,null,n);var l=t.memoizedState;if(r=l.element,i.isDehydrated){if(i={element:r,isDehydrated:!1,cache:l.cache,pendingSuspenseBoundaries:l.pendingSuspenseBoundaries,transitions:l.transitions},t.updateQueue.baseState=i,t.memoizedState=i,256&t.flags){t=Ol(e,t,r,n,o=cl(Error(a(423)),t));break e}if(r!==o){t=Ol(e,t,r,n,o=cl(Error(a(424)),t));break e}for(oa=so(t.stateNode.containerInfo.firstChild),ra=t,aa=!0,ia=null,n=Xa(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(ha(),r===o){t=Vl(e,t,n);break e}kl(e,t,r,n)}t=t.child}return t;case 5:return ii(t),null===e&&ca(t),r=t.type,o=t.pendingProps,i=null!==e?e.memoizedProps:null,l=o.children,no(r,o)?l=null:null!==i&&no(r,i)&&(t.flags|=32),Cl(e,t),kl(e,t,l,n),t.child;case 6:return null===e&&ca(t),null;case 13:return Ml(e,t,n);case 4:return oi(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=Ja(t,null,r,n):kl(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,xl(e,t,r,o=t.elementType===r?o:ga(r,o),n);case 7:return kl(e,t,t.pendingProps,n),t.child;case 8:case 12:return kl(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,o=t.pendingProps,i=t.memoizedProps,l=o.value,Lo(ya,r._currentValue),r._currentValue=l,null!==i)if(lr(i.value,l)){if(i.children===o.children&&!To.current){t=Vl(e,t,n);break e}}else for(null!==(i=t.child)&&(i.return=t);null!==i;){var u=i.dependencies;if(null!==u){l=i.child;for(var s=u.firstContext;null!==s;){if(s.context===r){if(1===i.tag){(s=Ra(-1,n&-n)).tag=2;var c=i.updateQueue;if(null!==c){var d=(c=c.shared).pending;null===d?s.next=s:(s.next=d.next,d.next=s),c.pending=s}}i.lanes|=n,null!==(s=i.alternate)&&(s.lanes|=n),Ea(i.return,n,t),u.lanes|=n;break}s=s.next}}else if(10===i.tag)l=i.type===t.type?null:i.child;else if(18===i.tag){if(null===(l=i.return))throw Error(a(341));l.lanes|=n,null!==(u=l.alternate)&&(u.lanes|=n),Ea(l,n,t),l=i.sibling}else l=i.child;if(null!==l)l.return=i;else for(l=i;null!==l;){if(l===t){l=null;break}if(null!==(i=l.sibling)){i.return=l.return,l=i;break}l=l.return}i=l}kl(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=t.pendingProps.children,_a(t,n),r=r(o=Ca(o)),t.flags|=1,kl(e,t,r,n),t.child;case 14:return o=ga(r=t.type,t.pendingProps),Sl(e,t,r,o=ga(r.type,o),n);case 15:return El(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:ga(r,o),Wl(e,t),t.tag=1,jo(r)?(e=!0,Ao(t)):e=!1,_a(t,n),Wa(t,r,o),Qa(t,r,o,n),Nl(null,t,r,!0,e,n);case 19:return Hl(e,t,n);case 22:return _l(e,t,n)}throw Error(a(156,t.tag))};var Ks="function"==typeof reportError?reportError:function(e){console.error(e)};function Ys(e){this._internalRoot=e}function Gs(e){this._internalRoot=e}function Js(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function Xs(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType&&(8!==e.nodeType||" react-mount-point-unstable "!==e.nodeValue))}function Zs(){}function ec(e,t,n,r,o){var a=n._reactRootContainer;if(a){var i=a;if("function"==typeof o){var l=o;o=function(){var e=Vs(i);l.call(e)}}Ws(t,i,e,o)}else i=function(e,t,n,r,o){if(o){if("function"==typeof r){var a=r;r=function(){var e=Vs(i);a.call(e)}}var i=Hs(t,r,e,0,null,!1,0,"",Zs);return e._reactRootContainer=i,e[mo]=i.current,Ur(8===e.nodeType?e.parentNode:e),ds(),i}for(;o=e.lastChild;)e.removeChild(o);if("function"==typeof r){var l=r;r=function(){var e=Vs(u);l.call(e)}}var u=Ds(e,0,!1,null,0,!1,0,"",Zs);return e._reactRootContainer=u,e[mo]=u.current,Ur(8===e.nodeType?e.parentNode:e),ds((function(){Ws(t,u,n,r)})),u}(n,t,e,o,r);return Vs(i)}Gs.prototype.render=Ys.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(a(409));Ws(e,t,null,null)},Gs.prototype.unmount=Ys.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;ds((function(){Ws(null,e,null,null)})),t[mo]=null}},Gs.prototype.unstable_scheduleHydration=function(e){if(e){var t=Et();e={blockedOn:null,target:e,priority:t};for(var n=0;n<jt.length&&0!==t&&t<jt[n].priority;n++);jt.splice(n,0,e),0===n&&At(e)}},kt=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=dt(t.pendingLanes);0!==n&&(yt(t,1|n),os(t,Je()),0==(6&Nu)&&(Hu=Je()+500,Ho()))}break;case 13:ds((function(){var t=Ta(e,1);if(null!==t){var n=ts();rs(t,e,1,n)}})),qs(e,1)}},xt=function(e){if(13===e.tag){var t=Ta(e,134217728);null!==t&&rs(t,e,134217728,ts()),qs(e,134217728)}},St=function(e){if(13===e.tag){var t=ns(e),n=Ta(e,t);null!==n&&rs(n,e,t,ts()),qs(e,t)}},Et=function(){return bt},_t=function(e,t){var n=bt;try{return bt=e,t()}finally{bt=n}},xe=function(e,t,n){switch(t){case"input":if(X(e,n),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=xo(r);if(!o)throw Error(a(90));q(r),X(r,o)}}}break;case"textarea":ae(e,n);break;case"select":null!=(t=n.value)&&ne(e,!!n.multiple,t,!1)}},Pe=cs,Ne=ds;var tc={usingClientEntryPoint:!1,Events:[wo,ko,xo,Ce,Le,cs]},nc={findFiberByHostInstance:bo,bundleType:0,version:"18.2.0",rendererPackageName:"react-dom"},rc={bundleType:nc.bundleType,version:nc.version,rendererPackageName:nc.rendererPackageName,rendererConfig:nc.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:w.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return null===(e=Ve(e))?null:e.stateNode},findFiberByHostInstance:nc.findFiberByHostInstance||function(){return null},findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.2.0-next-9e3b772b8-20220608"};if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var oc=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!oc.isDisabled&&oc.supportsFiber)try{ot=oc.inject(rc),at=oc}catch(ce){}}t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=tc,t.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!Js(t))throw Error(a(200));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:x,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},t.createRoot=function(e,t){if(!Js(e))throw Error(a(299));var n=!1,r="",o=Ks;return null!=t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(r=t.identifierPrefix),void 0!==t.onRecoverableError&&(o=t.onRecoverableError)),t=Ds(e,1,!1,null,0,n,0,r,o),e[mo]=t.current,Ur(8===e.nodeType?e.parentNode:e),new Ys(t)},t.findDOMNode=function(e){if(null==e)return null;if(1===e.nodeType)return e;var t=e._reactInternals;if(void 0===t){if("function"==typeof e.render)throw Error(a(188));throw e=Object.keys(e).join(","),Error(a(268,e))}return null===(e=Ve(t))?null:e.stateNode},t.flushSync=function(e){return ds(e)},t.hydrate=function(e,t,n){if(!Xs(t))throw Error(a(200));return ec(null,e,t,!0,n)},t.hydrateRoot=function(e,t,n){if(!Js(e))throw Error(a(405));var r=null!=n&&n.hydratedSources||null,o=!1,i="",l=Ks;if(null!=n&&(!0===n.unstable_strictMode&&(o=!0),void 0!==n.identifierPrefix&&(i=n.identifierPrefix),void 0!==n.onRecoverableError&&(l=n.onRecoverableError)),t=Hs(t,null,e,1,null!=n?n:null,o,0,i,l),e[mo]=t.current,Ur(e),r)for(e=0;e<r.length;e++)o=(o=(n=r[e])._getVersion)(n._source),null==t.mutableSourceEagerHydrationData?t.mutableSourceEagerHydrationData=[n,o]:t.mutableSourceEagerHydrationData.push(n,o);return new Gs(t)},t.render=function(e,t,n){if(!Xs(t))throw Error(a(200));return ec(null,e,t,!1,n)},t.unmountComponentAtNode=function(e){if(!Xs(e))throw Error(a(40));return!!e._reactRootContainer&&(ds((function(){ec(null,null,e,!1,(function(){e._reactRootContainer=null,e[mo]=null}))})),!0)},t.unstable_batchedUpdates=cs,t.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Xs(n))throw Error(a(200));if(null==e||void 0===e._reactInternals)throw Error(a(38));return ec(e,t,n,!1,r)},t.version="18.2.0-next-9e3b772b8-20220608"},745:(e,t,n)=>{var r=n(935);t.s=r.createRoot,r.hydrateRoot},935:(e,t,n)=>{!function e(){if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(e){console.error(e)}}(),e.exports=n(448)},251:(e,t,n)=>{var r=n(294),o=Symbol.for("react.element"),a=Symbol.for("react.fragment"),i=Object.prototype.hasOwnProperty,l=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,u={key:!0,ref:!0,__self:!0,__source:!0};function s(e,t,n){var r,a={},s=null,c=null;for(r in void 0!==n&&(s=""+n),void 0!==t.key&&(s=""+t.key),void 0!==t.ref&&(c=t.ref),t)i.call(t,r)&&!u.hasOwnProperty(r)&&(a[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===a[r]&&(a[r]=t[r]);return{$$typeof:o,type:e,key:s,ref:c,props:a,_owner:l.current}}t.Fragment=a,t.jsx=s,t.jsxs=s},408:(e,t)=>{var n=Symbol.for("react.element"),r=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),i=Symbol.for("react.profiler"),l=Symbol.for("react.provider"),u=Symbol.for("react.context"),s=Symbol.for("react.forward_ref"),c=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),p=Symbol.iterator,h={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},m=Object.assign,v={};function g(e,t,n){this.props=e,this.context=t,this.refs=v,this.updater=n||h}function y(){}function b(e,t,n){this.props=e,this.context=t,this.refs=v,this.updater=n||h}g.prototype.isReactComponent={},g.prototype.setState=function(e,t){if("object"!=typeof e&&"function"!=typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},g.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},y.prototype=g.prototype;var w=b.prototype=new y;w.constructor=b,m(w,g.prototype),w.isPureReactComponent=!0;var k=Array.isArray,x=Object.prototype.hasOwnProperty,S={current:null},E={key:!0,ref:!0,__self:!0,__source:!0};function _(e,t,r){var o,a={},i=null,l=null;if(null!=t)for(o in void 0!==t.ref&&(l=t.ref),void 0!==t.key&&(i=""+t.key),t)x.call(t,o)&&!E.hasOwnProperty(o)&&(a[o]=t[o]);var u=arguments.length-2;if(1===u)a.children=r;else if(1<u){for(var s=Array(u),c=0;c<u;c++)s[c]=arguments[c+2];a.children=s}if(e&&e.defaultProps)for(o in u=e.defaultProps)void 0===a[o]&&(a[o]=u[o]);return{$$typeof:n,type:e,key:i,ref:l,props:a,_owner:S.current}}function C(e){return"object"==typeof e&&null!==e&&e.$$typeof===n}var L=/\/+/g;function P(e,t){return"object"==typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,(function(e){return t[e]}))}(""+e.key):t.toString(36)}function N(e,t,o,a,i){var l=typeof e;"undefined"!==l&&"boolean"!==l||(e=null);var u=!1;if(null===e)u=!0;else switch(l){case"string":case"number":u=!0;break;case"object":switch(e.$$typeof){case n:case r:u=!0}}if(u)return i=i(u=e),e=""===a?"."+P(u,0):a,k(i)?(o="",null!=e&&(o=e.replace(L,"$&/")+"/"),N(i,t,o,"",(function(e){return e}))):null!=i&&(C(i)&&(i=function(e,t){return{$$typeof:n,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(i,o+(!i.key||u&&u.key===i.key?"":(""+i.key).replace(L,"$&/")+"/")+e)),t.push(i)),1;if(u=0,a=""===a?".":a+":",k(e))for(var s=0;s<e.length;s++){var c=a+P(l=e[s],s);u+=N(l,t,o,c,i)}else if(c=function(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=p&&e[p]||e["@@iterator"])?e:null}(e),"function"==typeof c)for(e=c.call(e),s=0;!(l=e.next()).done;)u+=N(l=l.value,t,o,c=a+P(l,s++),i);else if("object"===l)throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return u}function T(e,t,n){if(null==e)return e;var r=[],o=0;return N(e,r,"","",(function(e){return t.call(n,e,o++)})),r}function O(e){if(-1===e._status){var t=e._result;(t=t()).then((function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)}),(function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)})),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var I={current:null},j={transition:null},R={ReactCurrentDispatcher:I,ReactCurrentBatchConfig:j,ReactCurrentOwner:S};t.Children={map:T,forEach:function(e,t,n){T(e,(function(){t.apply(this,arguments)}),n)},count:function(e){var t=0;return T(e,(function(){t++})),t},toArray:function(e){return T(e,(function(e){return e}))||[]},only:function(e){if(!C(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=g,t.Fragment=o,t.Profiler=i,t.PureComponent=b,t.StrictMode=a,t.Suspense=c,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=R,t.cloneElement=function(e,t,r){if(null==e)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var o=m({},e.props),a=e.key,i=e.ref,l=e._owner;if(null!=t){if(void 0!==t.ref&&(i=t.ref,l=S.current),void 0!==t.key&&(a=""+t.key),e.type&&e.type.defaultProps)var u=e.type.defaultProps;for(s in t)x.call(t,s)&&!E.hasOwnProperty(s)&&(o[s]=void 0===t[s]&&void 0!==u?u[s]:t[s])}var s=arguments.length-2;if(1===s)o.children=r;else if(1<s){u=Array(s);for(var c=0;c<s;c++)u[c]=arguments[c+2];o.children=u}return{$$typeof:n,type:e.type,key:a,ref:i,props:o,_owner:l}},t.createContext=function(e){return(e={$$typeof:u,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:l,_context:e},e.Consumer=e},t.createElement=_,t.createFactory=function(e){var t=_.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:s,render:e}},t.isValidElement=C,t.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:O}},t.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=j.transition;j.transition={};try{e()}finally{j.transition=t}},t.unstable_act=function(){throw Error("act(...) is not supported in production builds of React.")},t.useCallback=function(e,t){return I.current.useCallback(e,t)},t.useContext=function(e){return I.current.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e){return I.current.useDeferredValue(e)},t.useEffect=function(e,t){return I.current.useEffect(e,t)},t.useId=function(){return I.current.useId()},t.useImperativeHandle=function(e,t,n){return I.current.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return I.current.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return I.current.useLayoutEffect(e,t)},t.useMemo=function(e,t){return I.current.useMemo(e,t)},t.useReducer=function(e,t,n){return I.current.useReducer(e,t,n)},t.useRef=function(e){return I.current.useRef(e)},t.useState=function(e){return I.current.useState(e)},t.useSyncExternalStore=function(e,t,n){return I.current.useSyncExternalStore(e,t,n)},t.useTransition=function(){return I.current.useTransition()},t.version="18.2.0"},294:(e,t,n)=>{e.exports=n(408)},893:(e,t,n)=>{e.exports=n(251)},53:(e,t)=>{function n(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,o=e[r];if(!(0<a(o,t)))break e;e[r]=t,e[n]=o,n=r}}function r(e){return 0===e.length?null:e[0]}function o(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,o=e.length,i=o>>>1;r<i;){var l=2*(r+1)-1,u=e[l],s=l+1,c=e[s];if(0>a(u,n))s<o&&0>a(c,u)?(e[r]=c,e[s]=n,r=s):(e[r]=u,e[l]=n,r=l);else{if(!(s<o&&0>a(c,n)))break e;e[r]=c,e[s]=n,r=s}}}return t}function a(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if("object"==typeof performance&&"function"==typeof performance.now){var i=performance;t.unstable_now=function(){return i.now()}}else{var l=Date,u=l.now();t.unstable_now=function(){return l.now()-u}}var s=[],c=[],d=1,f=null,p=3,h=!1,m=!1,v=!1,g="function"==typeof setTimeout?setTimeout:null,y="function"==typeof clearTimeout?clearTimeout:null,b="undefined"!=typeof setImmediate?setImmediate:null;function w(e){for(var t=r(c);null!==t;){if(null===t.callback)o(c);else{if(!(t.startTime<=e))break;o(c),t.sortIndex=t.expirationTime,n(s,t)}t=r(c)}}function k(e){if(v=!1,w(e),!m)if(null!==r(s))m=!0,j(x);else{var t=r(c);null!==t&&R(k,t.startTime-e)}}function x(e,n){m=!1,v&&(v=!1,y(C),C=-1),h=!0;var a=p;try{for(w(n),f=r(s);null!==f&&(!(f.expirationTime>n)||e&&!N());){var i=f.callback;if("function"==typeof i){f.callback=null,p=f.priorityLevel;var l=i(f.expirationTime<=n);n=t.unstable_now(),"function"==typeof l?f.callback=l:f===r(s)&&o(s),w(n)}else o(s);f=r(s)}if(null!==f)var u=!0;else{var d=r(c);null!==d&&R(k,d.startTime-n),u=!1}return u}finally{f=null,p=a,h=!1}}"undefined"!=typeof navigator&&void 0!==navigator.scheduling&&void 0!==navigator.scheduling.isInputPending&&navigator.scheduling.isInputPending.bind(navigator.scheduling);var S,E=!1,_=null,C=-1,L=5,P=-1;function N(){return!(t.unstable_now()-P<L)}function T(){if(null!==_){var e=t.unstable_now();P=e;var n=!0;try{n=_(!0,e)}finally{n?S():(E=!1,_=null)}}else E=!1}if("function"==typeof b)S=function(){b(T)};else if("undefined"!=typeof MessageChannel){var O=new MessageChannel,I=O.port2;O.port1.onmessage=T,S=function(){I.postMessage(null)}}else S=function(){g(T,0)};function j(e){_=e,E||(E=!0,S())}function R(e,n){C=g((function(){e(t.unstable_now())}),n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){m||h||(m=!0,j(x))},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):L=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return p},t.unstable_getFirstCallbackNode=function(){return r(s)},t.unstable_next=function(e){switch(p){case 1:case 2:case 3:var t=3;break;default:t=p}var n=p;p=t;try{return e()}finally{p=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=p;p=e;try{return t()}finally{p=n}},t.unstable_scheduleCallback=function(e,o,a){var i=t.unstable_now();switch(a="object"==typeof a&&null!==a&&"number"==typeof(a=a.delay)&&0<a?i+a:i,e){case 1:var l=-1;break;case 2:l=250;break;case 5:l=1073741823;break;case 4:l=1e4;break;default:l=5e3}return e={id:d++,callback:o,priorityLevel:e,startTime:a,expirationTime:l=a+l,sortIndex:-1},a>i?(e.sortIndex=a,n(c,e),null===r(s)&&e===r(c)&&(v?(y(C),C=-1):v=!0,R(k,a-i))):(e.sortIndex=l,n(s,e),m||h||(m=!0,j(x))),e},t.unstable_shouldYield=N,t.unstable_wrapCallback=function(e){var t=p;return function(){var n=p;p=t;try{return e.apply(this,arguments)}finally{p=n}}}},840:(e,t,n)=>{e.exports=n(53)},379:e=>{var t=[];function n(e){for(var n=-1,r=0;r<t.length;r++)if(t[r].identifier===e){n=r;break}return n}function r(e,r){for(var a={},i=[],l=0;l<e.length;l++){var u=e[l],s=r.base?u[0]+r.base:u[0],c=a[s]||0,d="".concat(s," ").concat(c);a[s]=c+1;var f=n(d),p={css:u[1],media:u[2],sourceMap:u[3],supports:u[4],layer:u[5]};if(-1!==f)t[f].references++,t[f].updater(p);else{var h=o(p,r);r.byIndex=l,t.splice(l,0,{identifier:d,updater:h,references:1})}i.push(d)}return i}function o(e,t){var n=t.domAPI(t);return n.update(e),function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap&&t.supports===e.supports&&t.layer===e.layer)return;n.update(e=t)}else n.remove()}}e.exports=function(e,o){var a=r(e=e||[],o=o||{});return function(e){e=e||[];for(var i=0;i<a.length;i++){var l=n(a[i]);t[l].references--}for(var u=r(e,o),s=0;s<a.length;s++){var c=n(a[s]);0===t[c].references&&(t[c].updater(),t.splice(c,1))}a=u}}},569:e=>{var t={};e.exports=function(e,n){var r=function(e){if(void 0===t[e]){var n=document.querySelector(e);if(window.HTMLIFrameElement&&n instanceof window.HTMLIFrameElement)try{n=n.contentDocument.head}catch(e){n=null}t[e]=n}return t[e]}(e);if(!r)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");r.appendChild(n)}},216:e=>{e.exports=function(e){var t=document.createElement("style");return e.setAttributes(t,e.attributes),e.insert(t,e.options),t}},565:(e,t,n)=>{e.exports=function(e){var t=n.nc;t&&e.setAttribute("nonce",t)}},795:e=>{e.exports=function(e){if("undefined"==typeof document)return{update:function(){},remove:function(){}};var t=e.insertStyleElement(e);return{update:function(n){!function(e,t,n){var r="";n.supports&&(r+="@supports (".concat(n.supports,") {")),n.media&&(r+="@media ".concat(n.media," {"));var o=void 0!==n.layer;o&&(r+="@layer".concat(n.layer.length>0?" ".concat(n.layer):""," {")),r+=n.css,o&&(r+="}"),n.media&&(r+="}"),n.supports&&(r+="}");var a=n.sourceMap;a&&"undefined"!=typeof btoa&&(r+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(a))))," */")),t.styleTagTransform(r,e,t.options)}(t,e,n)},remove:function(){!function(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e)}(t)}}}},589:e=>{e.exports=function(e,t){if(t.styleSheet)t.styleSheet.cssText=e;else{for(;t.firstChild;)t.removeChild(t.firstChild);t.appendChild(document.createTextNode(e))}}}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var a=t[r]={id:r,exports:{}};return e[r](a,a.exports,n),a.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.nc=void 0;var r={};(()=>{n.d(r,{default:()=>Ho});var e,t,o=n(893);(t=e||(e={})).ASC="ASC",t.DESC="DESC",t.CUSTOM="CUSTOM",t.AS_DEFINED_IN_SOURCE="AS_DEFINED_IN_SOURCE";const a=(e,t)=>t.split(".").reduce(((e,t)=>null==e?void 0:e[t]),e),i=(e,t,n)=>{const r=t.split(".");r.reduce(((e,t,o)=>(void 0===e[t]&&(e[t]={}),o===r.length-1&&(e[t]=n),e[t])),e)},l=e=>"value"in e,u=e=>e?e.flatMap((e=>(e=>"decoration"===e.type)(e)?[]:(e=>"group"===e.type)(e)?u(e.styles):l(e)?e:u(e.styles))):[],s=e=>{const t=u(e).filter(l),n={};return t.forEach((e=>{n[e.key]=(n[e.key]||0)+1})),Object.entries(n).filter((([,e])=>e>1))},c=(e,t,n=!0)=>{if(!e)return"";if(!t)return e;const r={"{LOGGED_IN_USER:EMAIL}":t.softr_user_email||"","{LOGGED_IN_USER:NAME}":t.softr_user_full_name||"","{LOGGED_IN_USER_RECORD_ID}":t.record_id||""};let o=e;for(const[e,t]of Object.entries(r)){const r=n?encodeURIComponent(String(t)):t;o=o.replaceAll(e,String(r))}const a=new RegExp("{LOGGED_IN_USER_FIELD:(.*?)}","g");return o=o.replace(a,((e,r)=>t[r]?n?encodeURIComponent(String(t[r])):String(t[r]):"")),o},d=e=>{"test"!==(void 0).MODE&&console.error(`[Block definition violation] ${e}`)},f=e=>null!=e&&"object"==typeof e&&"field"in e,p=e=>((e=>{(e=>{var t,n,r;const o=null===(n=null===(t=null==e?void 0:e.elements)||void 0===t?void 0:t.container)||void 0===n?void 0:n.styles;if(o){const e=s(o);if(e.length)throw e.forEach((([e])=>d(`Control with key "${e}" appears more than once in block styles`))),new Error("You cannot have duplicate control keys in block styles")}(null===(r=null==e?void 0:e.elements)||void 0===r?void 0:r.definitions)&&Object.values(e.elements.definitions).filter(f).forEach((e=>{const t=s(e.styles||[]);if(t.length)throw t.forEach((([t])=>d(`Control with key "${t}" appears more than once in field item "${e.field.type}" styles`))),new Error(`You cannot have duplicate control keys in field "${e.field.type}" item styles`)}))})(e)})(e),Object.assign(Object.assign({},e),{hrid:e.hrid||""}));p.defaults={hrid:"",device_visibility:["laptop","tablet","mobile"],enabled:!0,visibility:{user_group:{predefined_user_group_type:"ALL_USERS",custom_user_group_ids:[],enabled_predefined_user_group_types:["ALL_USERS","LOGGED_IN_USERS","NON_LOGGED_IN_USERS"]},devices:["laptop","tablet","mobile"]}},p.collectionDefaults={dataSources:[],condition:{expressions:[],logicalOperator:"AND"},noMatchStrategy:"EMPTY_STATE",sortOptions:[{field:"",direction:"asc"}],searchOptions:{fields:[]},actions:[]},p.collectionDataSourceDefaults={type:null,airtable:{url:"",baseId:"",tableName:"",viewName:""},softr:{applicationId:""},isComplete:!1};const h=({controlType:e,allowed:t,label:n})=>(r,o={})=>(((e,t,n)=>{if(t.some((e=>!n.includes(e.controlType))))throw new Error(`${e} may only contain ${n.map((e=>JSON.stringify(e))).join(", ")} controls inside styles`)})(e,r,t),Object.assign({type:"control",controlType:e,label:n,styles:r},o));function m(e){const t=Array.isArray(e)?e:[e];if(t.length>1&&t.some((e=>g.includes(e))))throw new Error("Cannot mix global values with other values in font-family");return t.map((e=>v.includes(e)||g.includes(e)?e:`'${e}'`)).join(", ")}h({controlType:"border",allowed:["borderStyle","borderWidth","color"],label:"Border"}),h({controlType:"background",allowed:["select","color","image"],label:"Background"}),h({controlType:"font",allowed:["fontFamily","color","fontWeight"],label:"Font"});const v=["generic(kai)","generic(fangsong)","generic(nastaliq)","serif","sans-serif","system-ui","cursive","fantasy","math","monospace","ui-serif","ui-sans-serif","ui-monospace","ui-rounded","emoji","fangsong"],g=["inherit","initial","revert","revert-layer","unset"],y={THEME:(e,t,n)=>{if("THEME"!==t.fallback.type)throw new Error("assignThemeFallback can only be used on theme fallback.");const r=a(n,t.fallback.key);if(r)return i(e,t.key,r)},VALUE:(e,t)=>{if("VALUE"!==t.fallback.type)throw new Error("assignValueFallback can only be used on value fallback.");i(e,t.key,t.fallback.value)}},b=(e,t,n)=>{if(null!==t.value)return i(e,t.key,t.value);(0,y[t.fallback.type])(e,t,n)},w=(e,{styles:t},n)=>{t.forEach((t=>k(e,t,n)))},k=(e,t,n)=>{const r=S[t.controlType];if(!r)throw new Error(`Unknown control type: "${t.controlType}"`);r(e,t,n)},x={align:b,boolean:b,color:b,opacity:b,padding:b,shadow:b,size:b,width:b,height:b,roundness:b,borderStyle:b,borderWidth:b,border:w,background:w,font:w,colorPalette:b,icon:b,image:b,fontWeight:b,aspectRatio:b,position:b,placement:b,shape:b,select:b,pixelRange:b,radio:b},S=Object.assign(Object.assign({},x),{fontFamily:(e,t,n)=>{if(null!==t.value){const n=m(t.value);return i(e,t.key,n)}if("THEME"===t.fallback.type){const r=a(n,t.fallback.key);if(null!=r&&"string"!=typeof r)throw new Error("Expected font family theme fallback to be a string or null/undefined, but got: "+typeof r);if(!r)return;return i(e,t.key,r)}if("VALUE"===t.fallback.type){const n=m(t.fallback.value);return i(e,t.key,n)}throw new Error(`Invalid font family fallback type: ${JSON.stringify(t.fallback)}`)}});Object.assign(Object.assign({},x),{fontFamily:b});var E,_=((E={config:{colors:{primary:"#152237",accent:"#3278FF",background:"#FFFFFF"},typography:{headingFontFamily:"Inter, sans-serif",headingFontWeight:600,bodyFontFamily:"Inter, sans-serif",bodyFontWeight:400},size:"M",roundness:"STANDARD",backgroundStyle:"STANDARD",shadow:!0,containerMaxWidth:"1852px"},tokens:{colors:{background:{default:"#FFFFFF",complementary:"#FFFFFF",skeleton:"#f2f2f2"},text:{heading:"#152237",description:"#434e5f",ui:"#152237"},border:{primary:"#e5e5e5",secondary:"#f3f5f8"},action:{highlight:"#3278FF",primary:{text:"#f4f8ff",background:{idle:"#3278FF",hover:"#2860cc",press:"#1e4899"}},secondary:{background:{idle:"#FFFFFF",hover:"#f2f2f2",press:"#e5e5e5"},text:"#152237"},dialogSecondary:{background:{idle:"#f2f2f2",hover:"#e5e5e5",press:"#ccc"},text:"#152237"},ghost:{text:"#152237",background:{idle:"transparent",hover:"#f2f2f2",press:"#e5e5e5"},border:{active:"#3278FF"}},disabled:{background:"#F2F2F2",text:"#898989"}},dialog:{background:{idle:"#FFFFFF",hover:"#f2f2f2",press:"#e5e5e5"}},input:{background:{idle:"#f2f2f2",hover:"#e5e5e5",focus:"#f2f2f2"},text:"#152237",placeholder:"#727a87",border:{focus:"#3278FF",error:"#c52c26"},message:{error:"#c52c26",hint:"#727a87"}},rating:{fill:"#e5e5e5",star:{color:"#FFB92D",borderColor:"#CFA042"},heart:{color:"#DA486B",borderColor:"#AC475D"}},layerCard:{layer1Background:"#f2f2f2",layer2Background:"#e5e5e5"},icon:"#727a87",system:{alert:{background:"#FFFFFF",title:"#232323",description:"#595959",icon:{success:"#00A352",warning:"#F68A0B",error:"#E90C17"}}}},typography:{heading:{fontFamily:"Inter, sans-serif",fontWeight:"600",size:{1:{fontSize:"1.25rem",lineHeight:"1.75rem"},2:{fontSize:"1.125rem",lineHeight:"1.5rem"},3:{fontSize:"1rem",lineHeight:"1.25rem"},title:{fontSize:"1.5rem",lineHeight:"2rem"}}},body:{fontFamily:"Inter, sans-serif",fontWeight:"400",fontSize:"0.875rem",lineHeight:"1.25rem"},text:{hint:{fontSize:"0.75rem",lineHeight:"1rem"},label:{fontSize:"0.875rem",lineHeight:"1.25rem"},footnote:{fontSize:"0.75rem",lineHeight:"1rem"}}},iconSize:{1:"14px",2:"16px",3:"20px",4:"24px",5:"40px"},roundness:{1:"4px",2:"6px",3:"8px",4:"12px",5:"16px",full:"9999px"},space:{0:"0px",1:"2px",2:"4px",3:"6px",4:"8px",5:"10px",6:"12px",7:"16px",8:"20px",9:"24px",10:"32px",11:"48px",12:"64px",13:"72px",14:"96px",15:"144px"},gap:void 0,shadow:{none:"0 0 #0000",subtle:"0px 2px 3px -2px rgba(0,0,0,0.12), 0px 2px 5px -2px rgba(0,0,0,0.08)",raised:"0px 16px 28px -4px rgba(0,0,0,0.02), 0px 5px 12px -3px rgba(0,0,0,0.08), 0px 2px 3px -2px rgba(0,0,0,0.06), 0px 0.5px 1px -1px rgba(0,0,0,0.06)",floating:"0px 10px 10px 0 rgba(0,0,0,0.04), 0px 20px 25px 0 rgba(0,0,0,0.06)"},containerMaxWidth:"1852px",components:{navigation:{topbarHeight:{regular:"64px",compact:"56px"},colors:{regular:{surface:"#FFFFFF",surfaceHover:"#f2f2f2",surfacePress:"#eaf1ff",surfaceActive:"#eaf1ff",textPrimary:"#152237",textSecondary:"#434e5f",textActive:"#3278FF",separator:"#f2f2f2",logo:"#3278FF",buttonPrimaryBackground:"#3278FF",buttonPrimaryText:"#fff"},accent:{surface:"#3278FF",surfaceHover:"#2f72f2",surfacePress:"#2d6ce5",surfaceActive:"#2d6ce5",textPrimary:"#f4f8ff",textSecondary:"#eaf1ff",textActive:"#fff",separator:"#84aeff",logo:"#eaf1ff",buttonPrimaryBackground:"#fff",buttonPrimaryText:"#091732"},softAccent:{surface:"#f4f8ff",surfaceHover:"#eaf1ff",surfacePress:"#e0eaff",surfaceActive:"#e0eaff",textPrimary:"#143066",textSecondary:"rgba(20,48,102,0.7)",textActive:"#2860cc",separator:"rgba(20,48,102,0.05)",logo:"#3278FF",buttonPrimaryBackground:"#3278FF",buttonPrimaryText:"#fff"},gray:{surface:"#f2f2f2",surfaceHover:"#f2f2f2",surfacePress:"#eaf1ff",surfaceActive:"#eaf1ff",textPrimary:"#152237",textSecondary:"#434e5f",textActive:"#3278FF",separator:"#e5e5e5",logo:"#3278FF",buttonPrimaryBackground:"#3278FF",buttonPrimaryText:"#fff"}}},button:{regular:{verticalPadding:"10px",horizontalPadding:"16px"},compact:{verticalPadding:"6px",horizontalPadding:"12px"}},input:{verticalPadding:"10px",horizontalPadding:"12px"},altIcon:{background:"#eaf1ff",color:"#2860cc"}}}}).tokens.gap=E.tokens.space,E);const C=(e,t)=>{const n={};for(const r of Object.keys(e)){const o=e[r];n[r]="object"==typeof o?C(o,t?`${t}.${r}`:r):{themeKey:t?`${t}.${r}`:r}}return n};C(_);const L=()=>window.location.href.startsWith("http://localhost:4200")||window.location.href.startsWith("https://studio-staging")||window.location.href.startsWith("https://studio.softr.io/"),P={HORIZONTAL_HEADER_CLASS_NAME:"spr-horizontal-header"};CustomEvent;var N=n(745),T=n(294);function O(e){return"compiled_markup"in e&&""!==e.compiled_markup}var I=n(935),j=function(e,t,n,r){return new(n||(n=Promise))((function(o,a){function i(e){try{u(r.next(e))}catch(e){a(e)}}function l(e){try{u(r.throw(e))}catch(e){a(e)}}function u(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(i,l)}u((r=r.apply(e,t||[])).next())}))};const R=new URLSearchParams(window.location.search).get("isViteBuildEnabled");let F;F=null!==R?"true"===R:"boolean"==typeof window.isViteBuildEnabled?window.isViteBuildEnabled:!!(window.location.hostname.match(/studio-staging(\d*)?\.softr\.io$/)||window.location.hostname.match(/preview\.staging(\d*)?\.softr\.app$/)||window.location.hostname.match(/localhost/));const z=new Map;function A(e){return j(this,void 0,void 0,(function*(){const t=yield fetch(e);if(!t.ok)throw new Error(`Failed to fetch ${e}: ${t.status} ${t.statusText}`);return t.json()}))}function M({name:e,version:t,baseUrl:n,enabled:r=!0}){const[o,a]=(0,T.useState)({status:"loading"});switch((0,T.useEffect)((()=>{if(!r)return;const o=new AbortController;return function(e,t,n){const r=z.get(`${e}/${t}`);if(r)return r;{const r=function(e,t,n){return j(this,void 0,void 0,(function*(){const r=`${n}/${e}/${t}/manifest.json?crossorigin&v=0.0.1`;try{return A(r)}catch(r){if(-1!==n.indexOf("localhost"))return console.error(`Failed to fetch manifest: ${e}/${t} from ${n}`,r),console.info("Trying to fetch manifest from prod"),A(`https://assets.softr-files.com/softr-blocks/prod/blocks/${e}/${t}/manifest.json`);throw r}}))}(e,t,n);return z.set(`${e}/${t}`,r),r}}(e,t,n).then((e=>{o.signal.aborted||a({status:"loaded",manifest:e})})).catch((e=>{o.signal.aborted||a({status:"error",error:e})})),()=>o.abort()}),[e,t,n,r]),o.status){case"loading":return null;case"error":throw o.error;case"loaded":return o.manifest;default:!function(e){throw new Error(`Unreachable: ${e}`)}(o)}}class B{constructor(e,t=e.firstChild){this.root=e,this.lastNode=t}positionAt(e){this.lastNode=e}appendChild(e){this.lastNode=$(this.root,e,this.lastNode)}moveChildToCursor(e){this.lastNode=$(this.root,e,this.lastNode)}}function $(e,t,n){var r;return e.insertBefore(t,null!==(r=null==n?void 0:n.nextSibling)&&void 0!==r?r:null),t}function D(e,t){if(e.parentNode!==t.parentNode)throw new Error("Cannot get nodes between nodes with different parents");let n=e.nextSibling;const r=[];for(r.push(e);n&&n!==t;)r.push(n),n=n.nextSibling;return r.push(t),r}const U={info(...e){console.log("INFO","SPR",...e)},debug(...e){console.debug("DEBUG","SPR",...e)},error(...e){console.error("ERROR","SPR",...e)},warn(...e){console.warn("WARN","SPR",...e)}};function H(e){return document.querySelector(`[data-spr-block-id="${e}"]`)}const W=["conditionalForm","forgot-password","reset-password","signIn","signUp","account-settings"];function V(e){const t=document.createElement("div");return e.name.toLowerCase().startsWith("header")&&!e.elements.verticalEnabled&&t.classList.add(P.HORIZONTAL_HEADER_CLASS_NAME),t.id=e.hrid,t.classList.add(`block-${e.id}`),t.setAttribute("category",e.category),t.setAttribute("data-block",Q(e)),t.setAttribute("data-spr-block-id",e.id),t.setAttribute("data-spr-role","block-root"),t.setAttribute(ie,e.hrid),W.includes(e.name)&&t.classList.add("dynamic-height"),t}function Q(e){return`${e.name}-v${e.version.replace(/\./g,"-")}`}function q(e){return Array.from(e.querySelectorAll("[data-spr-role=block-root]"))}function K(e){return document.querySelector(`[data-spr-block-id="${e}"]`)}function Y(e,t){const n=G(t),r=new B(e);for(let e=0;e<n.length;e++){const t=n[e],o=n[e-1];if(o?r.positionAt(re(o)):r.positionAt(null),O(t)){if(te(t)){const e=ee(t),n=ne(t);for(const t of D(e,n))r.moveChildToCursor(t);continue}U.debug("Adding sentinels for pre-compiled block",t.hrid),r.appendChild(X(t)),r.appendChild(Z(t));continue}const a=H(t.id);if(a)a.id!==t.hrid&&(U.debug(`Block hrid has changed from ${a.id} to ${t.hrid}`),a.id=t.hrid,a.setAttribute(ie,t.hrid)),r.moveChildToCursor(a);else{const e=V(t);U.debug("Adding block root for dynamic block",t.hrid),r.appendChild(e)}}}function G(e){const t=J(e);return e.filter((e=>!t[e.id]))}function J(e){const t={};for(const n of e)if("container"===n.type)for(const{blockId:e}of n.container.slots)e&&(t[e]=n.id);return t}function X(e){const t=document.createElement("div");return t.setAttribute("data-spr-role","sentinel-start"),t.setAttribute("data-spr-sentinel-start-for",e.id),t.setAttribute(ie,e.hrid),t}function Z(e){const t=document.createElement("div");return t.setAttribute("data-spr-role","sentinel-end"),t.setAttribute("data-spr-sentinel-end-for",e.id),t.setAttribute(ie,e.hrid),t}function ee(e){const t=document.querySelector(`[data-spr-sentinel-start-for="${e.id}"]`);if(!t)throw new Error(`Sentinel start not found for pre-compiled block: ${e.id}`);return t}function te(e){return null!==document.querySelector(`[data-spr-sentinel-start-for="${e.id}"]`)}function ne(e){const t=document.querySelector(`[data-spr-sentinel-end-for="${e.id}"]`);if(!t)throw new Error(`Sentinel start not found for pre-compiled block: ${e.id}`);return t}function re(e){if(O(e))return ne(e);const t=H(e.id);if(!t)throw new Error("Block root not found");return t}function oe(e){const t=[];for(const n of e.querySelectorAll("[data-spr-sentinel-start-for]")){const r=n.getAttribute("data-spr-sentinel-start-for");if(!r)throw new Error("Sentinel start has no block id");const o=e.querySelector(`[data-spr-sentinel-end-for="${r}"]`);if(!o)throw new Error("Sentinel end not found for sentinel start");t.push([r,n,o])}return t}function ae(e){return[ee(e),ne(e)]}const ie="data-spr-block-hrid",le="@softr-page-renderer/DOMContentLoaded",ue="@softr-page-renderer/load",se="@softr-page-renderer/ReactComponentReady";function ce(e){const t=t=>{e(t)};return window.addEventListener(se,t),{dispose(){window.removeEventListener(se,t)}}}const de=3,fe=3e3,pe=5e3,he={hiddenBlocks:[],pendingBlocks:[],blockWithLoader:null,firstNotLoadedBlock:null};function me(e){const t=!L(),[n,r]=(0,T.useState)((()=>e.reduce(((e,t)=>(e[t.id]=O(t),e)),{}))),[o,a]=(0,T.useState)({}),[i,l]=(0,T.useState)(!1);(0,T.useEffect)((()=>{if(!t)return;const{dispose:e}=ce((e=>{r((t=>Object.assign(Object.assign({},t),{[e.detail.blockId]:!0})))}));return e}),[t]);const u=(0,T.useMemo)((()=>{if(!t)return he;const r=function(e){const t=function(e){return e.reduce(((e,t)=>(e.set(t.id,t),e)),new Map)}(e);function n(e){var r,o,a;const i=e&&t.get(e);if(!i)return[];if("container"!==i.type)return[e];let l=null,u=i.container.slots;if("TAB_CONTAINER"===i.container.settings.layout.type){u=i.container.slots.filter((e=>{const n=e.blockId&&t.get(e.blockId);return!!n&&"container"===n.type&&n.container.slots.some((({blockId:e})=>!(!e||!t.get(e))))}));const e=u.find((e=>{const n=e.blockId?t.get(e.blockId):null;return(null==n?void 0:n.hrid)===ve}));l=null!==(a=null!==(r=null==e?void 0:e.blockId)&&void 0!==r?r:null===(o=u[0])||void 0===o?void 0:o.blockId)&&void 0!==a?a:null}return[e,...u.filter((e=>!l||e.blockId===l)).flatMap((e=>n(e.blockId)))]}return G(e).flatMap((e=>n(e.id))).map((e=>t.get(e))).filter((e=>!!e&&"Footer"!==e.category))}(e),a=r.filter((e=>"container"!==e.type&&"Header"!==e.category)).slice(0,de).every((e=>n[e.id]||o[e.id])),l=[],u=[];let s=null,c=null,d=null,f=0,p=!0;return r.forEach((e=>{!p||n[e.id]||o[e.id]?p||l.push(e.id):(c=e.id,s=e.id,p=!1,"container"===(null==d?void 0:d.type)&&d.container.slots.find((t=>t.blockId===e.id))&&(s=d.id,l.push(e.id))),d=e,"container"!==e.type&&"Header"!==e.category&&(f+=1,!a&&f>de&&u.push(e.id))})),i||(s=null),{hiddenBlocks:l,pendingBlocks:u,blockWithLoader:s,firstNotLoadedBlock:c}}),[n,o,e,t,i]),{firstNotLoadedBlock:s}=u;return(0,T.useEffect)((()=>{if(!s)return;const e=setTimeout((()=>{a((e=>Object.assign(Object.assign({},e),{[s]:!0})))}),pe);return()=>clearTimeout(e)}),[s]),(0,T.useEffect)((()=>{const e=setTimeout((()=>{l(!0)}),fe);return()=>clearTimeout(e)}),[]),u}const ve=window.location.hash.slice(1),ge="object"==typeof global&&global&&global.Object===Object&&global;var ye="object"==typeof self&&self&&self.Object===Object&&self;const be=ge||ye||Function("return this")(),we=be.Symbol;var ke=Object.prototype,xe=ke.hasOwnProperty,Se=ke.toString,Ee=we?we.toStringTag:void 0;var _e=Object.prototype.toString;var Ce=we?we.toStringTag:void 0;const Le=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":Ce&&Ce in Object(e)?function(e){var t=xe.call(e,Ee),n=e[Ee];try{e[Ee]=void 0;var r=!0}catch(e){}var o=Se.call(e);return r&&(t?e[Ee]=n:delete e[Ee]),o}(e):function(e){return _e.call(e)}(e)},Pe=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)},Ne=function(e){if(!Pe(e))return!1;var t=Le(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t},Te=be["__core-js_shared__"];var Oe,Ie=(Oe=/[^.]+$/.exec(Te&&Te.keys&&Te.keys.IE_PROTO||""))?"Symbol(src)_1."+Oe:"";var je=Function.prototype.toString;const Re=function(e){if(null!=e){try{return je.call(e)}catch(e){}try{return e+""}catch(e){}}return""};var Fe=/^\[object .+?Constructor\]$/,ze=Function.prototype,Ae=Object.prototype,Me=ze.toString,Be=Ae.hasOwnProperty,$e=RegExp("^"+Me.call(Be).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");const De=function(e){return!(!Pe(e)||(t=e,Ie&&Ie in t))&&(Ne(e)?$e:Fe).test(Re(e));var t},Ue=function(e,t){var n=function(e,t){return null==e?void 0:e[t]}(e,t);return De(n)?n:void 0},He=function(){try{var e=Ue(Object,"defineProperty");return e({},"",{}),e}catch(e){}}(),We=function(e,t,n,r){for(var o=-1,a=null==e?0:e.length;++o<a;){var i=e[o];t(r,i,n(i),e)}return r},Ve=function(e,t,n){for(var r=-1,o=Object(e),a=n(e),i=a.length;i--;){var l=a[++r];if(!1===t(o[l],l,o))break}return e},Qe=function(e){return null!=e&&"object"==typeof e},qe=function(e){return Qe(e)&&"[object Arguments]"==Le(e)};var Ke=Object.prototype,Ye=Ke.hasOwnProperty,Ge=Ke.propertyIsEnumerable;const Je=qe(function(){return arguments}())?qe:function(e){return Qe(e)&&Ye.call(e,"callee")&&!Ge.call(e,"callee")},Xe=Array.isArray;var Ze="object"==typeof exports&&exports&&!exports.nodeType&&exports,et=Ze&&"object"==typeof module&&module&&!module.nodeType&&module,tt=et&&et.exports===Ze?be.Buffer:void 0;const nt=(tt?tt.isBuffer:void 0)||function(){return!1};var rt=/^(?:0|[1-9]\d*)$/;const ot=function(e,t){var n=typeof e;return!!(t=null==t?9007199254740991:t)&&("number"==n||"symbol"!=n&&rt.test(e))&&e>-1&&e%1==0&&e<t},at=function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=9007199254740991};var it={};it["[object Float32Array]"]=it["[object Float64Array]"]=it["[object Int8Array]"]=it["[object Int16Array]"]=it["[object Int32Array]"]=it["[object Uint8Array]"]=it["[object Uint8ClampedArray]"]=it["[object Uint16Array]"]=it["[object Uint32Array]"]=!0,it["[object Arguments]"]=it["[object Array]"]=it["[object ArrayBuffer]"]=it["[object Boolean]"]=it["[object DataView]"]=it["[object Date]"]=it["[object Error]"]=it["[object Function]"]=it["[object Map]"]=it["[object Number]"]=it["[object Object]"]=it["[object RegExp]"]=it["[object Set]"]=it["[object String]"]=it["[object WeakMap]"]=!1;var lt="object"==typeof exports&&exports&&!exports.nodeType&&exports,ut=lt&&"object"==typeof module&&module&&!module.nodeType&&module,st=ut&&ut.exports===lt&&ge.process,ct=function(){try{return ut&&ut.require&&ut.require("util").types||st&&st.binding&&st.binding("util")}catch(e){}}(),dt=ct&&ct.isTypedArray;const ft=dt?(pt=dt,function(e){return pt(e)}):function(e){return Qe(e)&&at(e.length)&&!!it[Le(e)]};var pt,ht=Object.prototype.hasOwnProperty;const mt=function(e,t){var n=Xe(e),r=!n&&Je(e),o=!n&&!r&&nt(e),a=!n&&!r&&!o&&ft(e),i=n||r||o||a,l=i?function(e,t){for(var n=-1,r=Array(e);++n<e;)r[n]=t(n);return r}(e.length,String):[],u=l.length;for(var s in e)!t&&!ht.call(e,s)||i&&("length"==s||o&&("offset"==s||"parent"==s)||a&&("buffer"==s||"byteLength"==s||"byteOffset"==s)||ot(s,u))||l.push(s);return l};var vt=Object.prototype;const gt=function(e,t){return function(n){return e(t(n))}}(Object.keys,Object);var yt=Object.prototype.hasOwnProperty;const bt=function(e){if(n=(t=e)&&t.constructor,t!==("function"==typeof n&&n.prototype||vt))return gt(e);var t,n,r=[];for(var o in Object(e))yt.call(e,o)&&"constructor"!=o&&r.push(o);return r},wt=function(e){return null!=e&&at(e.length)&&!Ne(e)},kt=function(e){return wt(e)?mt(e):bt(e)},xt=function(e,t){if(null==e)return e;if(!wt(e))return function(e,t){return e&&Ve(e,t,kt)}(e,t);for(var n=e.length,r=-1,o=Object(e);++r<n&&!1!==t(o[r],r,o););return e},St=function(e,t,n,r){return xt(e,(function(e,o,a){t(r,e,n(e),a)})),r},Et=function(e,t){return e===t||e!=e&&t!=t},_t=function(e,t){for(var n=e.length;n--;)if(Et(e[n][0],t))return n;return-1};var Ct=Array.prototype.splice;function Lt(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}Lt.prototype.clear=function(){this.__data__=[],this.size=0},Lt.prototype.delete=function(e){var t=this.__data__,n=_t(t,e);return!(n<0||(n==t.length-1?t.pop():Ct.call(t,n,1),--this.size,0))},Lt.prototype.get=function(e){var t=this.__data__,n=_t(t,e);return n<0?void 0:t[n][1]},Lt.prototype.has=function(e){return _t(this.__data__,e)>-1},Lt.prototype.set=function(e,t){var n=this.__data__,r=_t(n,e);return r<0?(++this.size,n.push([e,t])):n[r][1]=t,this};const Pt=Lt,Nt=Ue(be,"Map"),Tt=Ue(Object,"create");var Ot=Object.prototype.hasOwnProperty;var It=Object.prototype.hasOwnProperty;function jt(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}jt.prototype.clear=function(){this.__data__=Tt?Tt(null):{},this.size=0},jt.prototype.delete=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t},jt.prototype.get=function(e){var t=this.__data__;if(Tt){var n=t[e];return"__lodash_hash_undefined__"===n?void 0:n}return Ot.call(t,e)?t[e]:void 0},jt.prototype.has=function(e){var t=this.__data__;return Tt?void 0!==t[e]:It.call(t,e)},jt.prototype.set=function(e,t){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=Tt&&void 0===t?"__lodash_hash_undefined__":t,this};const Rt=jt,Ft=function(e,t){var n,r,o=e.__data__;return("string"==(r=typeof(n=t))||"number"==r||"symbol"==r||"boolean"==r?"__proto__"!==n:null===n)?o["string"==typeof t?"string":"hash"]:o.map};function zt(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}zt.prototype.clear=function(){this.size=0,this.__data__={hash:new Rt,map:new(Nt||Pt),string:new Rt}},zt.prototype.delete=function(e){var t=Ft(this,e).delete(e);return this.size-=t?1:0,t},zt.prototype.get=function(e){return Ft(this,e).get(e)},zt.prototype.has=function(e){return Ft(this,e).has(e)},zt.prototype.set=function(e,t){var n=Ft(this,e),r=n.size;return n.set(e,t),this.size+=n.size==r?0:1,this};const At=zt;function Mt(e){var t=this.__data__=new Pt(e);this.size=t.size}Mt.prototype.clear=function(){this.__data__=new Pt,this.size=0},Mt.prototype.delete=function(e){var t=this.__data__,n=t.delete(e);return this.size=t.size,n},Mt.prototype.get=function(e){return this.__data__.get(e)},Mt.prototype.has=function(e){return this.__data__.has(e)},Mt.prototype.set=function(e,t){var n=this.__data__;if(n instanceof Pt){var r=n.__data__;if(!Nt||r.length<199)return r.push([e,t]),this.size=++n.size,this;n=this.__data__=new At(r)}return n.set(e,t),this.size=n.size,this};const Bt=Mt;function $t(e){var t=-1,n=null==e?0:e.length;for(this.__data__=new At;++t<n;)this.add(e[t])}$t.prototype.add=$t.prototype.push=function(e){return this.__data__.set(e,"__lodash_hash_undefined__"),this},$t.prototype.has=function(e){return this.__data__.has(e)};const Dt=$t,Ut=function(e,t){for(var n=-1,r=null==e?0:e.length;++n<r;)if(t(e[n],n,e))return!0;return!1},Ht=function(e,t,n,r,o,a){var i=1&n,l=e.length,u=t.length;if(l!=u&&!(i&&u>l))return!1;var s=a.get(e),c=a.get(t);if(s&&c)return s==t&&c==e;var d=-1,f=!0,p=2&n?new Dt:void 0;for(a.set(e,t),a.set(t,e);++d<l;){var h=e[d],m=t[d];if(r)var v=i?r(m,h,d,t,e,a):r(h,m,d,e,t,a);if(void 0!==v){if(v)continue;f=!1;break}if(p){if(!Ut(t,(function(e,t){if(i=t,!p.has(i)&&(h===e||o(h,e,n,r,a)))return p.push(t);var i}))){f=!1;break}}else if(h!==m&&!o(h,m,n,r,a)){f=!1;break}}return a.delete(e),a.delete(t),f},Wt=be.Uint8Array,Vt=function(e){var t=-1,n=Array(e.size);return e.forEach((function(e,r){n[++t]=[r,e]})),n},Qt=function(e){var t=-1,n=Array(e.size);return e.forEach((function(e){n[++t]=e})),n};var qt=we?we.prototype:void 0,Kt=qt?qt.valueOf:void 0;var Yt=Object.prototype.propertyIsEnumerable,Gt=Object.getOwnPropertySymbols;const Jt=Gt?function(e){return null==e?[]:(e=Object(e),function(e,t){for(var n=-1,r=null==e?0:e.length,o=0,a=[];++n<r;){var i=e[n];t(i,n,e)&&(a[o++]=i)}return a}(Gt(e),(function(t){return Yt.call(e,t)})))}:function(){return[]},Xt=function(e){return function(e,t,n){var r=t(e);return Xe(e)?r:function(e,t){for(var n=-1,r=t.length,o=e.length;++n<r;)e[o+n]=t[n];return e}(r,n(e))}(e,kt,Jt)};var Zt=Object.prototype.hasOwnProperty;const en=Ue(be,"DataView"),tn=Ue(be,"Promise"),nn=Ue(be,"Set"),rn=Ue(be,"WeakMap");var on="[object Map]",an="[object Promise]",ln="[object Set]",un="[object WeakMap]",sn="[object DataView]",cn=Re(en),dn=Re(Nt),fn=Re(tn),pn=Re(nn),hn=Re(rn),mn=Le;(en&&mn(new en(new ArrayBuffer(1)))!=sn||Nt&&mn(new Nt)!=on||tn&&mn(tn.resolve())!=an||nn&&mn(new nn)!=ln||rn&&mn(new rn)!=un)&&(mn=function(e){var t=Le(e),n="[object Object]"==t?e.constructor:void 0,r=n?Re(n):"";if(r)switch(r){case cn:return sn;case dn:return on;case fn:return an;case pn:return ln;case hn:return un}return t});const vn=mn;var gn="[object Arguments]",yn="[object Array]",bn="[object Object]",wn=Object.prototype.hasOwnProperty;const kn=function(e,t,n,r,o,a){var i=Xe(e),l=Xe(t),u=i?yn:vn(e),s=l?yn:vn(t),c=(u=u==gn?bn:u)==bn,d=(s=s==gn?bn:s)==bn,f=u==s;if(f&&nt(e)){if(!nt(t))return!1;i=!0,c=!1}if(f&&!c)return a||(a=new Bt),i||ft(e)?Ht(e,t,n,r,o,a):function(e,t,n,r,o,a,i){switch(n){case"[object DataView]":if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case"[object ArrayBuffer]":return!(e.byteLength!=t.byteLength||!a(new Wt(e),new Wt(t)));case"[object Boolean]":case"[object Date]":case"[object Number]":return Et(+e,+t);case"[object Error]":return e.name==t.name&&e.message==t.message;case"[object RegExp]":case"[object String]":return e==t+"";case"[object Map]":var l=Vt;case"[object Set]":var u=1&r;if(l||(l=Qt),e.size!=t.size&&!u)return!1;var s=i.get(e);if(s)return s==t;r|=2,i.set(e,t);var c=Ht(l(e),l(t),r,o,a,i);return i.delete(e),c;case"[object Symbol]":if(Kt)return Kt.call(e)==Kt.call(t)}return!1}(e,t,u,n,r,o,a);if(!(1&n)){var p=c&&wn.call(e,"__wrapped__"),h=d&&wn.call(t,"__wrapped__");if(p||h){var m=p?e.value():e,v=h?t.value():t;return a||(a=new Bt),o(m,v,n,r,a)}}return!!f&&(a||(a=new Bt),function(e,t,n,r,o,a){var i=1&n,l=Xt(e),u=l.length;if(u!=Xt(t).length&&!i)return!1;for(var s=u;s--;){var c=l[s];if(!(i?c in t:Zt.call(t,c)))return!1}var d=a.get(e),f=a.get(t);if(d&&f)return d==t&&f==e;var p=!0;a.set(e,t),a.set(t,e);for(var h=i;++s<u;){var m=e[c=l[s]],v=t[c];if(r)var g=i?r(v,m,c,t,e,a):r(m,v,c,e,t,a);if(!(void 0===g?m===v||o(m,v,n,r,a):g)){p=!1;break}h||(h="constructor"==c)}if(p&&!h){var y=e.constructor,b=t.constructor;y==b||!("constructor"in e)||!("constructor"in t)||"function"==typeof y&&y instanceof y&&"function"==typeof b&&b instanceof b||(p=!1)}return a.delete(e),a.delete(t),p}(e,t,n,r,o,a))},xn=function e(t,n,r,o,a){return t===n||(null==t||null==n||!Qe(t)&&!Qe(n)?t!=t&&n!=n:kn(t,n,r,o,e,a))},Sn=function(e){return e==e&&!Pe(e)},En=function(e,t){return function(n){return null!=n&&n[e]===t&&(void 0!==t||e in Object(n))}},_n=function(e){var t=function(e){for(var t=kt(e),n=t.length;n--;){var r=t[n],o=e[r];t[n]=[r,o,Sn(o)]}return t}(e);return 1==t.length&&t[0][2]?En(t[0][0],t[0][1]):function(n){return n===e||function(e,t,n,r){var o=n.length,a=o,i=!r;if(null==e)return!a;for(e=Object(e);o--;){var l=n[o];if(i&&l[2]?l[1]!==e[l[0]]:!(l[0]in e))return!1}for(;++o<a;){var u=(l=n[o])[0],s=e[u],c=l[1];if(i&&l[2]){if(void 0===s&&!(u in e))return!1}else{var d=new Bt;if(r)var f=r(s,c,u,e,t,d);if(!(void 0===f?xn(c,s,3,r,d):f))return!1}}return!0}(n,e,t)}},Cn=function(e){return"symbol"==typeof e||Qe(e)&&"[object Symbol]"==Le(e)};var Ln=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,Pn=/^\w*$/;const Nn=function(e,t){if(Xe(e))return!1;var n=typeof e;return!("number"!=n&&"symbol"!=n&&"boolean"!=n&&null!=e&&!Cn(e))||Pn.test(e)||!Ln.test(e)||null!=t&&e in Object(t)};function Tn(e,t){if("function"!=typeof e||null!=t&&"function"!=typeof t)throw new TypeError("Expected a function");var n=function(){var r=arguments,o=t?t.apply(this,r):r[0],a=n.cache;if(a.has(o))return a.get(o);var i=e.apply(this,r);return n.cache=a.set(o,i)||a,i};return n.cache=new(Tn.Cache||At),n}Tn.Cache=At;var On=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,In=/\\(\\)?/g;const jn=(Rn=Tn((function(e){var t=[];return 46===e.charCodeAt(0)&&t.push(""),e.replace(On,(function(e,n,r,o){t.push(r?o.replace(In,"$1"):n||e)})),t}),(function(e){return 500===Fn.size&&Fn.clear(),e})),Fn=Rn.cache,Rn);var Rn,Fn;var zn=we?we.prototype:void 0,An=zn?zn.toString:void 0;const Mn=function e(t){if("string"==typeof t)return t;if(Xe(t))return function(e,t){for(var n=-1,r=null==e?0:e.length,o=Array(r);++n<r;)o[n]=t(e[n],n,e);return o}(t,e)+"";if(Cn(t))return An?An.call(t):"";var n=t+"";return"0"==n&&1/t==-1/0?"-0":n},Bn=function(e,t){return Xe(e)?e:Nn(e,t)?[e]:jn(function(e){return null==e?"":Mn(e)}(e))},$n=function(e){if("string"==typeof e||Cn(e))return e;var t=e+"";return"0"==t&&1/e==-1/0?"-0":t},Dn=function(e,t){for(var n=0,r=(t=Bn(t,e)).length;null!=e&&n<r;)e=e[$n(t[n++])];return n&&n==r?e:void 0},Un=function(e,t){return null!=e&&t in Object(e)},Hn=function(e,t){return null!=e&&function(e,t,n){for(var r=-1,o=(t=Bn(t,e)).length,a=!1;++r<o;){var i=$n(t[r]);if(!(a=null!=e&&n(e,i)))break;e=e[i]}return a||++r!=o?a:!!(o=null==e?0:e.length)&&at(o)&&ot(i,o)&&(Xe(e)||Je(e))}(e,t,Un)},Wn=function(e,t){return Nn(e)&&Sn(t)?En($n(e),t):function(n){var r=function(e,t,n){var r=null==e?void 0:Dn(e,t);return void 0===r?n:r}(n,e);return void 0===r&&r===t?Hn(n,e):xn(t,r,3)}},Vn=function(e){return e},Qn=function(e){return Nn(e)?(t=$n(e),function(e){return null==e?void 0:e[t]}):function(e){return function(t){return Dn(t,e)}}(e);var t},qn=(Kn=function(e,t,n){!function(e,t,n){"__proto__"==t&&He?He(e,t,{configurable:!0,enumerable:!0,value:n,writable:!0}):e[t]=n}(e,n,t)},function(e,t){var n,r={};return(Xe(e)?We:St)(e,Kn,"function"==typeof(n=t)?n:null==n?Vn:"object"==typeof n?Xe(n)?Wn(n[0],n[1]):_n(n):Qn(n),r)});var Kn;function Yn(e,t){return Object.keys(e).length===Object.keys(t).length&&Object.keys(e).every((n=>e[n]===t[n]))}const Gn={};function Jn(e){const t=qn(e,"id"),n=(0,T.useRef)({});for(const e of Object.values(t)){if("container"!==e.type){n.current[e.id]=Gn;continue}const r=n.current[e.id],o=Xn(e,t);r&&o&&Yn(o,r)||(n.current[e.id]=o)}return n.current}function Xn(e,t){if("container"!==e.type)return Gn;let n={};for(const r of e.container.slots){if(!r.blockId)continue;const e=t[r.blockId],o=e?Xn(e,t):Gn;n=Object.assign(Object.assign(Object.assign({},n),{[r.blockId]:e}),o)}return n}const Zn=window.location.hash.slice(1);function er({definitions:e,pendingBlocks:t,hiddenBlocks:n,firstNotLoadedBlock:r}){const o=(0,T.useCallback)(((o=Zn)=>{function a(e){return!!e&&e!==r&&!n.includes(e)&&!t.includes(e)}let i=e.find((e=>e.hrid===o));if("container"===(null==i?void 0:i.type)){const t=tr(i,e);a(null==t?void 0:t.id)?"TAB"===i.container.settings.layout.type&&(i=function(e,t){return t.find((t=>"container"===t.type&&!!t.container.slots.find((t=>t.blockId===e))))}(null==i?void 0:i.id,e)):i=void 0}return!a(null==i?void 0:i.id)||(l=null==i?void 0:i.id)&&0===e.filter((e=>"Header"!==e.category)).findIndex((e=>e.id===l))||null==i?void 0:i.hrid;var l}),[e,r,n,t]),a=(0,T.useMemo)((()=>o()),[o]);(0,T.useEffect)((()=>{nr(a)}),[a]),(0,T.useEffect)((()=>{function e(){nr(o(window.location.hash.slice(1)))}return window.addEventListener("hashchange",e),()=>{window.removeEventListener("hashchange",e)}}),[o])}function tr(e,t){var n;const r=null===(n=null==e?void 0:e.container.slots[0])||void 0===n?void 0:n.blockId,o=t.find((e=>e.id===r));return"container"===(null==o?void 0:o.type)?tr(o,t):o}function nr(e){if(L())return;const t=e?document.getElementById(e):null;null==t||t.scrollIntoView()}const rr=function(e,t){return xn(e,t)};function or(e){const t=(0,T.useRef)(e);return rr(e,t.current)||(t.current=e),t.current}var ar=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n};const ir=(0,T.createContext)(null);function lr(e){var{children:t,pageRef:n}=e,r=ar(e,["children","pageRef"]);const{definitions:a,pageId:i,theme:l,nestedBlocks:u}=r,s=or(l),[c,d]=function(){const e=(0,T.useRef)(null),t=()=>(e.current||(e.current=new Map),e.current),n=(0,T.useCallback)(((e,n)=>{const r=t();n?r.set(e,n):r.delete(e)}),[]);return[t(),n]}();(0,T.useImperativeHandle)(n,(()=>({blockInstanceMap:c})));const{hiddenBlocks:f,pendingBlocks:p,blockWithLoader:h}=me(a),m=or(a.map((e=>{var t,n;return[e.id,(null===(n=null===(t=e.collection)||void 0===t?void 0:t.dataSources)||void 0===n?void 0:n.map((e=>e.resourceIdentifier.keys)).flat())||[]]}))),v=(0,T.useMemo)((()=>new Map(m)),[m]),g=(0,T.useMemo)((()=>({pageId:i,setBlockInstanceRef:d,nestedBlocks:u,hiddenBlocks:f,pendingBlocks:p,blockWithLoader:h,theme:s,datasourceResourceIdentifiers:v})),[i,d,u,f,p,h,s,v]);return(0,o.jsx)(ir.Provider,{value:g,children:t})}function ur(e){return(0,T.useMemo)((()=>K(e)),[e])}function sr(){const e=(0,T.useContext)(ir);if(!e)throw new Error("usePageContext must be used within a PageContextProvider");return e}function cr(){const{theme:e}=sr(),t=(0,T.useMemo)((()=>dr(e)),[e]);return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("style",{dangerouslySetInnerHTML:{__html:t}}),(0,o.jsx)("div",{role:"progressbar",style:{display:"flex",height:"120px",justifyContent:"center",alignItems:"center"},children:(0,o.jsx)("div",{className:"spr-spinner"})})]})}const dr=e=>`\n.spr-spinner {\n    width: 36px;\n    height: 36px;\n    border: 2px solid color-mix(in srgb, ${e.defaultBodyTextColor} 30%, transparent 70%);\n    border-bottom-color: ${e.defaultButtonBackgroundColor};\n    border-radius: 50%;\n    display: inline-block;\n    box-sizing: border-box;\n    animation: rotation 1s linear infinite;\n}\n\n@keyframes rotation {\n    0% {\n        transform: rotate(0deg);\n    }\n    100% {\n        transform: rotate(360deg);\n    }\n}\n`,fr="data-spr-block-parent-id";function pr(){const e="style[data-spr-role='head-styles-sentinel']",t=document.querySelector(e);if(!t)throw new Error(`Could not find the element: ${e}; Make sure you call insertHeadStylesSentinel before calling this function.`);return t}const hr="data-spr-child-block-wrapper-id";function mr(e){var t,n,r="";if("string"==typeof e||"number"==typeof e)r+=e;else if("object"==typeof e)if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(n=mr(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function vr(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=mr(e))&&(r&&(r+=" "),r+=t);return r}const gr=(0,T.createContext)(null);function yr(){const e=(0,T.useContext)(gr);if(!e)throw new Error("useAppConstants must be used within an AppConstantsProvider");return e}var br=function(e,t,n,r){return new(n||(n=Promise))((function(o,a){function i(e){try{u(r.next(e))}catch(e){a(e)}}function l(e){try{u(r.throw(e))}catch(e){a(e)}}function u(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(i,l)}u((r=r.apply(e,t||[])).next())}))};function wr({definition:e,topLevel:t,childBlockRootProps:n,childBlocks:r}){const{hiddenBlocks:a,pendingBlocks:i,blockWithLoader:l,datasourceResourceIdentifiers:u,setBlockInstanceRef:s,theme:c,pageId:d}=sr(),{featureFlags:{fixedBlockRoots:f},baseUrl:p,lifecycle:h,blockIdAttributeName:m,slotIdAttributeName:v,slotBlockIdAttributeName:g,mode:y,appId:b,studioEventEmitter:w,spaMode:k}=yr(),{name:x,version:S}=e,E=Q(e),_=M({name:x,version:S,baseUrl:p,enabled:!i.includes(e.id)}),[C,L]=(0,T.useState)(!1),[N,O]=function({definition:e,baseUrl:t,manifest:n,dataBlock:r,areNavRootsReady:o,fixedBlockRoots:a}){const[{Component:i,styles:l},u]=(0,T.useState)({Component:null,styles:[]}),{hrid:s,name:c,version:d}=e,{lifecycle:f}=yr(),p="navigation"===c&&a&&!o;return(0,T.useEffect)((()=>{const e=new AbortController,{signal:r}=e;return function(){br(this,void 0,void 0,(function*(){if(p)return;if(!n)return;const[e,o]=yield function(e,t){return br(this,arguments,void 0,(function*(e,{name:t,version:n,baseUrl:r}){if("2"===e.version){const[o]=yield Promise.all([Sr({name:t,version:n,baseUrl:r,scriptName:e.main}),...e.css.map((e=>Er(e)))]);return[o,[]]}{const o=e.styles.map((e=>{return a=`${r}/${t}/${n}`,"."===(o=e)[0]&&"/"===o[1]?`${a}${o.substring(1)}`:o;var o,a}));return Promise.all([kr({name:t,version:n,baseUrl:r,scriptName:e.scripts.main}),Promise.all(o.map((e=>fetch(e).then((e=>e.text())))))])}}))}(n,{name:c,baseUrl:t,version:d});r.aborted||u({Component:e,styles:o})}))}(),()=>e.abort()}),[c,d,t,s,n,r,f,p]),[i,l]}({manifest:_,definition:e,baseUrl:p,dataBlock:E,lifecycle:h,areNavRootsReady:C,fixedBlockRoots:f});var I,j;I=e.hrid,j=a.includes(e.id),(0,T.useLayoutEffect)((()=>{const e=document.getElementById(I);null==e||e.classList.toggle("spr-hidden",j)}),[I,j]);const R=(0,T.useCallback)((t=>{s(e.id,t)}),[e.id,s]);(0,T.useImperativeHandle)(R,(()=>({api:null==N?void 0:N.api}))),(0,T.useLayoutEffect)((()=>{var t;N&&(t={blockId:e.id},window.dispatchEvent(new CustomEvent(se,{detail:t})))}),[N,e.id]),function(e,t){const{featureFlags:n,blockIdAttributeName:r}=yr(),{fixedBlockRoots:o}=n,a=ur(e.id),i=Q(e);(0,T.useLayoutEffect)((()=>{o||a&&a.getAttribute("data-block")!==i&&a.setAttribute("data-block",i)}),[a,i,o]),(0,T.useLayoutEffect)((()=>{if(!o&&a)return a.setAttribute(fr,e.id),()=>a.removeAttribute(fr)}),[e.id,a,o]),(0,T.useEffect)((()=>{if(!o&&a&&r)return"navigation"===e.name?(a.classList.add("spr-navigation-placeholder"),()=>{a.classList.remove("spr-navigation-placeholder")}):(t.topLevel&&a.setAttribute(r,e.id),()=>{t.topLevel&&a.removeAttribute(r)})}),[o,a,e.id,r,e.name,t.topLevel]),function(e,t){const{featureFlags:{fixedBlockRoots:n}}=yr();(0,T.useLayoutEffect)((()=>{if(n)return;if(!e)return;if("2"!==e.version)return;const r=e.cssScopeClassName,o=function(e){const t=K(e);if(t)return t;const n=document.querySelector(`[${hr}="${e}"]`);if(n&&!(n instanceof HTMLElement))throw new Error(`Element with ${hr}="${e}" is not an HTMLElement.`);return n}(t.id);if(null==o||o.classList.add(r),"navigation"===t.name){const e=document.getElementById("topbar-root"),t=document.getElementById("sidebar-root"),n=document.getElementById("bottombar-root");e&&e.classList.add(r),t&&t.classList.add(r),n&&n.classList.add(r)}return()=>{if(null==o||o.classList.remove(r),"navigation"===t.name){const e=document.getElementById("topbar-root"),t=document.getElementById("sidebar-root"),n=document.getElementById("bottombar-root");e&&e.classList.remove(r),t&&t.classList.remove(r),n&&n.classList.remove(r)}}}),[e,t.name,t.id,n])}(t.manifest,e)}(e,{topLevel:t,manifest:_});const F=`[${fr}="${e.id}"]`,z=(0,T.useMemo)((()=>O.map((e=>function(e,t){let n=function(e){let t=!1,n="";for(let r=0;r<e.length;r++)"/"!==e[r]||"*"!==e[r+1]?"*"!==e[r]||"/"!==e[r+1]?t||(n+=e[r]):(t=!1,r++):(t=!0,r++);return n}(e);const r=function(...e){const t=e[e.length-1];return new RegExp(e.map((e=>{return(t=e)instanceof RegExp?t.source:t;var t})).join(""),t instanceof RegExp?t.flags:void 0)}(/[@\w*#.:]/,/[^{}]+/,/{/g);return n=n.replace(r,(e=>"@"===e[0]||"*"===e[0]&&"!"===e[1]||0===e.indexOf("from")||0===e.indexOf("to")||e[0].match(/\d/)?e:e.split(",").map((e=>`${t} ${e}`)).join(","))),n}(e,F)))),[O,F]);(0,T.useLayoutEffect)((()=>{const e=[],t=pr();for(const n of z){const r=document.createElement("style");r.setAttribute("data-trace","React18Block.tsx"),r.innerHTML=n,t.before(r),e.push(r)}return()=>e.forEach((e=>e.remove()))}),[z]);const A=!N&&"studio"===y||l===e.id,B=(0,o.jsxs)(o.Fragment,{children:[A&&(0,o.jsx)(cr,{}),N&&(0,o.jsx)(N,Object.assign({},e,{pageId:d,appId:b,definition:e,defaultTheme:c,parentSelector:F,blocks:r,studioEventEmitter:w,slotBlockIdAttributeName:null!=g?g:void 0,slotIdAttributeName:null!=v?v:void 0,datasourceResourceIdentifiers:u,spaMode:k}))]});if(!f)return B;const $="2"===(null==_?void 0:_.version)?_.cssScopeClassName:"",D=e.name.toLowerCase().startsWith("header")&&!e.elements.verticalEnabled,U={"data-block":E,category:e.category,[fr]:e.id};return t&&m&&(U[m]=e.id),(0,o.jsxs)(o.Fragment,{children:["navigation"===e.name&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("div",{id:"topbar-root",className:$,ref:()=>L(!0)}),(0,o.jsx)("div",{id:"sidebar-root",className:$}),(0,o.jsx)("div",{id:"bottombar-root",className:$})]}),(0,o.jsx)("div",Object.assign({id:e.hrid,className:vr($,"navigation"===e.name&&"spr-navigation-placeholder",W.includes(e.name)&&"dynamic-height",D&&P.HORIZONTAL_HEADER_CLASS_NAME,null==n?void 0:n.className),style:null==n?void 0:n.style},U,{children:B}))]})}function kr(e){return br(this,arguments,void 0,(function*({name:e,version:t,baseUrl:n,scriptName:r}){const o=`${n}/${e}/${t}/${r}`,a=xr.get(o);if(a)return a;const i=function(e){return br(this,arguments,void 0,(function*({name:e,baseUrl:t,version:n,scriptName:r}){const o=yield function({baseUrl:e,name:t,version:n,scriptName:r}){return fetch(`${e}/${t}/${n}/${r}`).then((e=>e.text()))}({baseUrl:t,name:e,version:n,scriptName:r}),a=document.createElement("script");a.dataset.forBlockName=e,a.dataset.forBlockVersion=n,window.react18=T,window.react_dom18=I;const i=`SoftrBlock_${e}_${n}`,l=`\n        {\n            // React 18 blocks expect react and react-dom to be available\n            // as variables with this name\n            var react18 = window.react18;\n            var react_dom18 = window.react_dom18;\n            ${o};\n            if (typeof renderBlock !== "function") {\n                throw new Error("No renderBlock function found", ${JSON.stringify({name:e,version:n})});\n            }\n            // webpack builds expose a single global named renderBlock\n            window["${i}"] = renderBlock;\n        }\n    `;a.innerHTML=l,document.body.appendChild(a);const u=window[i];if("function"!=typeof u)throw new Error(`Expected window object to have a function at key ${i}; Found ${u}`);return u}))}({name:e,version:t,baseUrl:n,scriptName:r});return xr.set(o,i),i}))}wr.displayName="React18Block";const xr=new Map;function Sr(e){return br(this,arguments,void 0,(function*({name:e,baseUrl:t,version:n,scriptName:r}){const o=`${t}/${e}/${n}/${r.replace(/^\.\//,"")}`,a=t.replace(/\/blocks(\/)?$/,"");return window.SOFTR_BASE_URL=a,(yield import(o)).default}))}function Er(e){return br(this,void 0,void 0,(function*(){const t=window.SOFTR_BASE_URL+"/"+e;let n=document.querySelector(`link[href="${t}"]`);return n||(n=document.createElement("link"),n.rel="stylesheet",n.href=t,pr().before(n)),new Promise(((e,t)=>{n.sheet?e(void 0):(n.addEventListener("load",e),n.addEventListener("error",t))}))}))}function _r({definition:e,childBlocks:t,topLevel:n,childBlockRootProps:r}){const{blockWithLoader:a,pendingBlocks:i}=sr(),{baseUrl:l,mode:u}=yr(),s=M({name:e.name,version:e.version,baseUrl:l,enabled:!i.includes(e.id)}),c="studio"===u||a===e.id;return s?(0,o.jsx)(wr,{definition:e,childBlocks:t,topLevel:n,childBlockRootProps:r}):c&&(0,o.jsx)(cr,{})}function Cr(e){return e.name.startsWith("custom-code")}window.__softrCSSImport=Er,window.__softr_React=T,window.__softr_ReactDOM=I;var Lr=function(e,t,n,r){return new(n||(n=Promise))((function(o,a){function i(e){try{u(r.next(e))}catch(e){a(e)}}function l(e){try{u(r.throw(e))}catch(e){a(e)}}function u(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(i,l)}u((r=r.apply(e,t||[])).next())}))};function Pr(e){return Lr(this,void 0,void 0,(function*(){if(e instanceof HTMLScriptElement)yield Nr(e);else for(const t of Array.from(e.querySelectorAll("script")))yield Nr(t)}))}function Nr(e){return Lr(this,void 0,void 0,(function*(){var t;let n=Promise.resolve();const r=function(e){const t=document.createElement("script");for(const n of Array.from(e.attributes))t.setAttribute(n.name,n.value);return t.async=e.async,t.defer=e.defer,e.src||(t.innerHTML=e.innerHTML),t}(e);r.src&&!r.async&&(n=new Promise(((e,t)=>{r.addEventListener("load",e),r.addEventListener("error",(e=>t(e.error)))})));try{null===(t=e.parentNode)||void 0===t||t.replaceChild(r,e)}catch(e){console.error("Error running the script",e)}if(e.src&&!r.async)try{yield n}catch(e){console.error("Error loading script",e)}}))}var Tr=function(e,t,n,r){return new(n||(n=Promise))((function(o,a){function i(e){try{u(r.next(e))}catch(e){a(e)}}function l(e){try{u(r.throw(e))}catch(e){a(e)}}function u(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(i,l)}u((r=r.apply(e,t||[])).next())}))};function Or({definition:e,topLevel:t}){const{setBlockInstanceRef:n}=sr(),{featureFlags:{fixedBlockRoots:r}}=yr(),a=(0,T.useCallback)((t=>n(e.id,t)),[e.id,n]);return(0,T.useImperativeHandle)(a,(()=>({}))),r?Cr(e)?(0,o.jsx)(Ir,{definition:e,topLevel:t}):(0,o.jsx)(jr,{definition:e,topLevel:t}):t?(0,o.jsx)(Rr,{definition:e}):(0,o.jsx)(Fr,{definition:e})}function Ir({definition:e,topLevel:t}){const{mode:n,blockIdAttributeName:r,slotIdAttributeName:a}=yr(),{hiddenBlocks:i}=sr(),l=i.includes(e.id),u=(0,T.useRef)(null),s=(0,T.useRef)(!1),c=(0,T.useRef)(null),d=(0,T.useRef)(null),f=(0,T.useRef)(null),p=Q(e);if((0,T.useLayoutEffect)((()=>{var e;null===(e=f.current)||void 0===e||e.classList.toggle("spr-hidden",l)}),[l]),(0,T.useLayoutEffect)((()=>{if("studio"===n)return;if(s.current)return;s.current=!0;const t=c.current,r=d.current;if(!t||!r)throw new Error("Sentinel nodes not found; Are the refs attached?");const o=t.parentElement;if(!o)throw new Error("Orphan sentinel node found");const a=zr(new B(o,t),{compiled_markup:e.compiled_markup});for(const e of D(t,r))e instanceof Element&&Pr(e);const i=Ar(a);i&&i.setAttribute("data-block",p)}),[e.compiled_markup,n,p]),"studio"===n){const n={"data-block":Q(e)};return t&&r&&(n[r]=e.id),!t&&a&&(n[a]=e.id),(0,o.jsx)("div",Object.assign({id:e.hrid,dangerouslySetInnerHTML:{__html:Mr}},n))}return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("div",{"data-spr-hrid":e.hrid,"data-spr-role":"custom-code-start-sentinel",ref:c}),(0,o.jsx)("style",{"data-spr-hrid":e.hrid,"data-spr-role":"custom-code-compiled_style",dangerouslySetInnerHTML:{__html:e.compiled_style}}),(0,o.jsx)("script",{"data-spr-hrid":e.hrid,"data-spr-role":"custom-code-compiled_script",dangerouslySetInnerHTML:{__html:e.compiled_script},ref:u}),(0,o.jsx)("div",{"data-spr-hrid":e.hrid,"data-spr-role":"custom-code-end-sentinel",ref:d})]})}function jr({definition:e,topLevel:t}){const{hiddenBlocks:n}=sr(),{mode:r,blockIdAttributeName:a}=yr(),i=n.includes(e.id),l=(0,T.useRef)(null),u=(0,T.useRef)(null),s=(0,T.useRef)(null),c=(0,T.useRef)(!1);return(0,T.useLayoutEffect)((()=>{var e;null===(e=s.current)||void 0===e||e.classList.toggle("spr-hidden",i)}),[i]),(0,T.useLayoutEffect)((()=>{if("studio"===r)return;if(c.current)return;c.current=!0;const n=l.current,o=u.current;if(!n||!o)throw new Error("Sentinel nodes not found; Are the refs attached?");const i=n.parentElement;if(!i)throw new Error("Orphan sentinel node found");const d=zr(new B(i,n),{compiled_markup:e.compiled_markup});for(const e of D(n,o))e instanceof Element&&Pr(e);const f=Ar(d);f&&a&&t&&f.setAttribute(a,e.id),s.current=f}),[e.compiled_markup,r,a,e.id,t]),(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("div",{"data-spr-hrid":e.hrid,"data-spr-role":"sentinel-start",ref:l}),(0,o.jsx)("style",{"data-spr-hrid":e.hrid,"data-spr-role":"compiled_style",dangerouslySetInnerHTML:{__html:e.compiled_style}}),(0,o.jsx)("script",{"data-spr-hrid":e.hrid,"data-spr-role":"compiled_script",dangerouslySetInnerHTML:{__html:e.compiled_script}}),(0,o.jsx)("div",{"data-spr-hrid":e.hrid,"data-spr-role":"sentinel-end",ref:u})]})}function Rr({definition:e}){const t=(0,T.useMemo)((()=>ee({id:e.id})),[e.id]),{mode:n,blockIdAttributeName:r}=yr(),{hiddenBlocks:o}=sr(),a=(0,T.useRef)(null),i=o.includes(e.id);return(0,T.useLayoutEffect)((()=>{const o=t.parentElement;if(!o)throw new Error("Orphan sentinel node found");const i=new AbortController,l=[],u=new B(o,t),s={compiled_markup:e.compiled_markup,compiled_script:e.compiled_script,compiled_style:e.compiled_style,id:e.id,name:e.name,version:e.version};if("studio"===n&&Cr(s)){const e=document.createElement("div");e.id=s.id,e.setAttribute("data-block",Q(s)),e.setAttribute("data-block-id",e.id),e.innerHTML=Mr,u.appendChild(e),l.push(e)}else l.push(...zr(u,s),...function(e,{compiled_style:t}){const n=document.createElement("style");return n.innerHTML=t,e.appendChild(n),[n]}(u,s),...function(e,{compiled_script:t,id:n}){const r=[],o=document.createComment(`compiled_script for ${n}`);e.appendChild(o),r.push(o);const a=document.createElement("div");return a.innerHTML=`<script>${t}<\/script>`,Array.from(a.children).forEach((t=>{e.appendChild(t),r.push(t)})),r}(u,s));"studio"===n&&"map1"===s.name&&l.push(function(e){const t=document.createElement("script");return t.src="https://maps.googleapis.com/maps/api/js?key=",e.appendChild(t),t}(u));const c=Ar(l);return c&&r&&c.setAttribute(r,e.id),a.current=c,function(){Tr(this,void 0,void 0,(function*(){if(!Cr(s))for(const e of l){if(i.signal.aborted)return;e instanceof HTMLElement&&(yield Pr(e))}}))}(),()=>{var e;i.abort();for(const t of l)null===(e=t.parentNode)||void 0===e||e.removeChild(t);a.current=null}}),[t,e.name,e.compiled_markup,e.compiled_script,e.compiled_style,e.id,e.version,r,n]),(0,T.useLayoutEffect)((()=>{var e;null===(e=a.current)||void 0===e||e.classList.toggle("spr-hidden",i)}),[i]),null}function Fr({definition:e}){const t=(0,T.useRef)(null),n=(0,T.useRef)(null),{hiddenBlocks:r}=sr(),{mode:a}=yr(),i=r.includes(e.id),l=(0,T.useRef)(!1);if((0,T.useLayoutEffect)((()=>{const e=t.current,r=n.current;if("studio"!==a){if(!e||!r)throw new Error("Missing markup or script element; Are the refs attached?");l.current||(l.current=!0,"studio"!==a&&(Pr(e),Pr(r)))}}),[a]),!Cr(e))throw new Error("We only support custom code PreCompiledBlocks inside block containers at the moment.If you're seeing this error, this assumption has changed and we might need to update this component to execute the compiled_script even in the studio.");const u=window.logged_in_user;return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("div",{ref:t,style:{display:i?"none":void 0},dangerouslySetInnerHTML:{__html:"studio"===a?Mr:c(e.compiled_markup,u,!1)}}),"studio"!==a&&(0,o.jsx)("style",{dangerouslySetInnerHTML:{__html:e.compiled_style}}),"studio"!==a&&(0,o.jsx)("script",{ref:n,dangerouslySetInnerHTML:{__html:e.compiled_script}})]})}function zr(e,{compiled_markup:t}){const n=document.createElement("div"),r=window.logged_in_user;n.innerHTML=c(t,r,!1);const o=[];for(const t of Array.from(n.childNodes))e.appendChild(t),o.push(t);return o}function Ar(e){const t=["SCRIPT","STYLE","COMMENT"],n=e.filter((e=>!(e instanceof HTMLElement&&t.includes(e.tagName))));for(const e of n){if(e instanceof HTMLElement&&e.hasAttribute("data-block-id"))return e;if(e instanceof HTMLElement&&"NAV"===e.tagName)return e}for(const e of n)if(e instanceof HTMLElement)return e;return null}const Mr='\n    <div style="text-align: center; padding: 20px; font-weight: 500;">\n        Custom code does not currently render on the canvas. Open the preview mode to view it.\n        We\'re working on improving this experience.\n    </div>\n';(0,T.createContext)(null);const Br=(0,T.createContext)(null),$r={didCatch:!1,error:null};class Dr extends T.Component{constructor(e){super(e),this.resetErrorBoundary=this.resetErrorBoundary.bind(this),this.state=$r}static getDerivedStateFromError(e){return{didCatch:!0,error:e}}resetErrorBoundary(){const{error:e}=this.state;if(null!==e){for(var t,n,r=arguments.length,o=new Array(r),a=0;a<r;a++)o[a]=arguments[a];null===(t=(n=this.props).onReset)||void 0===t||t.call(n,{args:o,reason:"imperative-api"}),this.setState($r)}}componentDidCatch(e,t){var n,r;null===(n=(r=this.props).onError)||void 0===n||n.call(r,e,t)}componentDidUpdate(e,t){const{didCatch:n}=this.state,{resetKeys:r}=this.props;var o,a;n&&null!==t.error&&function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return e.length!==t.length||e.some(((e,n)=>!Object.is(e,t[n])))}(e.resetKeys,r)&&(null===(o=(a=this.props).onReset)||void 0===o||o.call(a,{next:r,prev:e.resetKeys,reason:"keys"}),this.setState($r))}render(){const{children:e,fallbackRender:t,FallbackComponent:n,fallback:r}=this.props,{didCatch:o,error:a}=this.state;let i=e;if(o){const e={error:a,resetErrorBoundary:this.resetErrorBoundary};if("function"==typeof t)i=t(e);else if(n)i=(0,T.createElement)(n,e);else{if(null!==r&&!(0,T.isValidElement)(r))throw a;i=r}}return(0,T.createElement)(Br.Provider,{value:{didCatch:o,error:a,resetErrorBoundary:this.resetErrorBoundary}},i)}}function Ur({children:e}){return(0,o.jsx)(Dr,{FallbackComponent:Hr,children:e})}function Hr({error:e,resetErrorBoundary:t}){return(0,T.useEffect)((()=>{console.error("ErrorBoundary caught an error:",e)}),[e]),null}(0,T.createContext)(null),(0,T.createContext)(null);const Wr=(0,T.memo)((function(e){const{mode:t}=yr(),n=(0,T.useMemo)((()=>"studio"===t?function(e){return"Header"!==e.category?e:e.elements.verticalEnabled||e.elements.sticky?Object.assign(Object.assign({},e),{elements:Object.assign(Object.assign({},e.elements),{verticalEnabled:!1,sticky:!1})}):e}(e.definition):e.definition),[e.definition,t]);return(0,o.jsx)(Vr,Object.assign({},e,{definition:n}))}));function Vr(e){const{definition:t}=e,{featureFlags:n}=yr();return O(t)?(0,o.jsx)(Ur,{children:(0,o.jsx)(Or,{definition:t,topLevel:e.topLevel})}):!n.fixedBlockRoots&&e.topLevel?(0,o.jsx)(Ur,{children:(0,o.jsx)(Qr,Object.assign({},e))}):(0,o.jsx)(Ur,{children:(0,o.jsx)(_r,Object.assign({},e))})}function Qr({definition:e,childBlocks:t,topLevel:n,childBlockRootProps:r}){const a=ur(e.id);if(!a)throw new Error("Block root not found for block: "+e.hrid);return(0,I.createPortal)((0,o.jsx)(_r,{definition:e,childBlocks:t,topLevel:n,childBlockRootProps:r}),a)}var qr=function(e,t,n,r){return new(n||(n=Promise))((function(o,a){function i(e){try{u(r.next(e))}catch(e){a(e)}}function l(e){try{u(r.throw(e))}catch(e){a(e)}}function u(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(i,l)}u((r=r.apply(e,t||[])).next())}))};const Kr=new Map;function Yr(e){return qr(this,void 0,void 0,(function*(){e=Gr(e);const t=Kr.get(e);if(t)return t;const n=function(e){return qr(this,void 0,void 0,(function*(){const t=yield fetch(e);let n=yield t.text();const r="SoftrPageRenderer.render(",o=n.indexOf(r);n=n.slice(o+25).split("\n")[0].trim();const a=n.indexOf(').then(() => window.dispatchEvent (new Event ("@softr/page-content-loaded")))'),i=n.match(/pageId: "([^"]+)"/)[1];return n=n.slice(0,a).trim(),n=n.split("blocks: ")[1],n=n.split(", targetElement: document.getElementById('page-content')")[0],{blocks:JSON.parse(n),pageId:i}}))}(e);return Kr.set(e,n),n}))}function Gr(e){return new URL(e).pathname}Yr.initializeCache=(e,t)=>{e=Gr(e),Kr.set(e,Promise.resolve(t))};var Jr=function(e,t,n,r){return new(n||(n=Promise))((function(o,a){function i(e){try{u(r.next(e))}catch(e){a(e)}}function l(e){try{u(r.throw(e))}catch(e){a(e)}}function u(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(i,l)}u((r=r.apply(e,t||[])).next())}))};const Xr=(0,T.createContext)(null),Zr=(0,T.forwardRef)((function(e,t){const{spaMode:n,featureFlags:r}=yr(),a=(0,T.useMemo)((()=>({theme:e.theme,pageRef:t})),[e.theme,t]);let i;return i=n||r.fixedBlockRoots?(0,o.jsx)(to,Object.assign({},e)):(0,o.jsx)(eo,Object.assign({},e,{ref:t})),(0,o.jsx)(Xr.Provider,{value:a,children:i})})),eo=(0,T.forwardRef)((function(e,t){const n=e.definitions,r=e.pageId,a=Jn(n),i=G(n),{hiddenBlocks:l,pendingBlocks:u,firstNotLoadedBlock:s}=me(n);return er({definitions:n,hiddenBlocks:l,pendingBlocks:u,firstNotLoadedBlock:s}),(0,o.jsx)(lr,{pageId:r,definitions:n,pageRef:t,nestedBlocks:a,theme:e.theme,children:i.map((e=>(0,o.jsx)(Wr,{definition:e,childBlocks:a[e.id],childBlockRootProps:null,topLevel:!0},`${e.id}-${e.name}-${e.version}`)))})})),to=e=>{const{definitions:t,pageId:n,isNavigating:r,navigationProgress:a}=function({pagePropsDefinitions:e,pagePropsPageId:t}){const{spaMode:n}=yr(),r=(0,T.useRef)(!1),[o,a]=(0,T.useState)(e),[i,l]=(0,T.useState)(t),[u,s]=(0,T.useState)(!1),[c,d]=(0,T.useState)(0);return(0,T.useEffect)((()=>{n&&Yr.initializeCache(oo,{pageId:t,blocks:e})}),[n,e,t]),(0,T.useEffect)((()=>{if(!n)return;const e=(e,t)=>Jr(this,void 0,void 0,(function*(){if(r.current)return;e.startsWith("/")&&(e=`${window.location.origin}${e}`),"PUSH"===t&&history.pushState({},"",e),s(!0),r.current=!0;const n=setInterval((()=>{d((e=>{const t=100-e;return Math.min(e+t/20,85)}))}),100);try{d(10);const{blocks:t,pageId:n}=yield Yr(e);document.getElementById("page-content").setAttribute("data-pageid",n),l(n),a(t),d(100)}finally{r.current=!1,s(!1),clearInterval(n)}})),t=()=>Jr(this,void 0,void 0,(function*(){e(window.location.href,"BACK")})),o=t=>Jr(this,void 0,void 0,(function*(){const n=t.detail.link;e(n,"PUSH")}));return window.addEventListener("page-renderer:spa-navigation",o),window.addEventListener("popstate",t),()=>{window.removeEventListener("softr:action-click",o),window.removeEventListener("popstate",t)}}),[n]),n?{definitions:o,pageId:i,isNavigating:u,navigationProgress:c}:{definitions:e,pageId:t,isNavigating:!1,navigationProgress:0}}({pagePropsDefinitions:e.definitions,pagePropsPageId:e.pageId});return(0,o.jsx)(no,{definitions:t,pageId:n,isNavigating:r,navigationProgress:a})};function no({definitions:e,pageId:t,isNavigating:n,navigationProgress:r}){const{featureFlags:a}=yr(),{theme:i,pageRef:l}=function(){const e=(0,T.useContext)(Xr);if(!e)throw new Error("useAppRootContext must be used within an AppRootContextProvider");return e}(),u=G(e),s=Jn(e),{hiddenBlocks:c,pendingBlocks:d,firstNotLoadedBlock:f}=me(e);er({definitions:e,hiddenBlocks:c,pendingBlocks:d,firstNotLoadedBlock:f});const p=i.defaultButtonBackgroundColor;if(a.navigationLayout){const a=e.find((e=>"navigation"===e.name)),c=u.filter((e=>"navigation"!==e.name));return(0,o.jsxs)(lr,{pageRef:l,pageId:t,definitions:e,nestedBlocks:s,theme:i,children:[a&&(0,o.jsx)(Wr,{definition:a,childBlocks:s[a.id],childBlockRootProps:null,topLevel:!0}),(0,o.jsxs)("main",{id:"main-content",children:[n&&(0,o.jsx)(ro,{progress:r,accent:p}),c.map((e=>(0,o.jsx)(Wr,{definition:e,childBlocks:s[e.id],childBlockRootProps:null,topLevel:!0},`${e.id}-${e.name}-${e.version}`)))]})]})}return(0,o.jsxs)(lr,{theme:i,definitions:e,pageId:t,pageRef:l,nestedBlocks:s,children:[n&&(0,o.jsx)(ro,{progress:r,accent:p}),u.map((e=>(0,o.jsx)(Wr,{definition:e,childBlocks:s[e.id],childBlockRootProps:null,topLevel:!0},`${e.id}-${e.name}-${e.version}`)))]})}function ro({progress:e,accent:t}){return(0,o.jsx)("div",{style:{zIndex:1e4,position:"fixed",top:0,left:0,height:"3px",backgroundColor:"transparent",width:"100%"},children:(0,o.jsx)("div",{style:{height:"100%",backgroundColor:t,width:`${e}%`,transition:"width 0.1s ease-in-out",boxShadow:`0 0 10px ${t}`}})})}const oo=window.location.href;function ao(e){return t=this,n=arguments,o=function*({at:e,code:t,debugLabel:n,lifecycle:r}){const o=document.createComment(`Start of ${n} custom code`);e.appendChild(o),r.push((()=>o.remove()));const a=document.createElement("div");a.innerHTML=t;for(const t of Array.from(a.childNodes))e.appendChild(t),r.push((()=>t.remove())),t instanceof Element&&(yield Pr(t));const i=document.createComment(`End of ${n} custom code`);e.appendChild(i),r.push((()=>i.remove()))},new((r=void 0)||(r=Promise))((function(e,a){function i(e){try{u(o.next(e))}catch(e){a(e)}}function l(e){try{u(o.throw(e))}catch(e){a(e)}}function u(t){var n;t.done?e(t.value):(n=t.value,n instanceof r?n:new r((function(e){e(n)}))).then(i,l)}u((o=o.apply(t,n||[])).next())}));var t,n,r,o}class io{constructor(){this.disposables=[]}push(e){"function"==typeof e&&(e={dispose:e}),this.disposables.push(e)}dispose(){this.disposables.forEach((e=>e.dispose())),this.disposables=[]}}const lo={DOMContentLoaded:le,load:ue};function uo(e,t){const n=t.addEventListener;t.addEventListener=function(r,o,a){const i=lo[r];i?(t.addEventListener(i,o,a),e.push((()=>{t.removeEventListener(i,o)}))):n.call(t,r,o,a)};const r=t.addEventListener;return()=>{t.addEventListener===r?t.addEventListener=n:console.error("addEventListener was patched by another script; Skipping the restoration of the page renderer patch.")}}const so={},co={},fo={},po="localhost"===window.location.hostname||/staging(\d{0,2})\.softr\.(io|app)$/.test(window.location.hostname);if(window.location.pathname.startsWith("/embed/"))try{window.sessionStorage.setItem("embeddedMode","true")}catch(e){U.warn("Failed to save 'embeddedMode' value to sessionStorage",e)}const ho="true"===window.sessionStorage.getItem("embeddedMode"),mo=!!(window.location.hostname.match(/studio-staging(\d*)?\.softr\.io$/)||window.location.hostname.match(/preview\.staging(\d*)?\.softr\.app$/)||window.location.hostname.match(/localhost/));var vo=n(379),go=n.n(vo),yo=n(795),bo=n.n(yo),wo=n(569),ko=n.n(wo),xo=n(565),So=n.n(xo),Eo=n(216),_o=n.n(Eo),Co=n(589),Lo=n.n(Co),Po=n(483),No={};No.styleTagTransform=Lo(),No.setAttributes=So(),No.insert=ko().bind(null,"head"),No.domAPI=bo(),No.insertStyleElement=_o(),go()(Po.Z,No),Po.Z&&Po.Z.locals&&Po.Z.locals;var To,Oo=function(e,t,n,r){return new(n||(n=Promise))((function(o,a){function i(e){try{u(r.next(e))}catch(e){a(e)}}function l(e){try{u(r.throw(e))}catch(e){a(e)}}function u(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(i,l)}u((r=r.apply(e,t||[])).next())}))};const Io=null!==(To=localStorage.getItem("NAV_TRANSITION_DURATION"))&&void 0!==To?To:"1.5s";(window.isNavigationTransitionsEnabled||localStorage.getItem("isNavigationTransitionsEnabled"))&&(U.info("Installing view transitions"),function(){const e=document.createElement("style");function t(e,t){return`\n          ::view-transition-group(${e}) {\n            animation-duration: ${t.duration};\n          }\n          ::view-transition-new(${e}) {\n            animation-name: ${t.newAnimation};\n          }\n          ::view-transition-old(${e}) {\n            animation-name: ${t.oldAnimation};\n          }\n        `}e.innerHTML=`\n      @view-transition {\n        navigation: auto;\n      }\n\n      @keyframes fade-out-with-delay {\n        0% {\n          opacity: 1;\n        }\n        /* delay the start of the animation\n         * If we start fading out immediately, the new sidebar/topbar will not be\n         * visible when we start fading out leading to\n         */\n        80% {\n          opacity: 1;\n        }\n        to {\n          opacity: 0;\n        }\n      }\n\n      @keyframes fade-in-with-delay {\n        0% {\n          opacity: 0;\n        }\n        10% {\n          opacity: 1;\n        }\n        to {\n          opacity: 1;\n        }\n      }\n\n\n      @keyframes fade-in {\n        0% {\n          opacity: 0;\n        }\n        100% {\n          opacity: 1;\n        }\n      }\n\n      @keyframes fade-out {\n        0% {\n          opacity: 1;\n        }\n        100% {\n          opacity: 0;\n        }\n      }\n\n      @keyframes fade-out-immediately {\n        from {\n          opacity: 0;\n        }\n        to {\n          opacity: 0;\n        }\n      }\n\n      #sidebar-root {\n        view-transition-name: sidebar;\n      }\n      ${t("sidebar",{newAnimation:"fade-in-with-delay",oldAnimation:"fade-out-with-delay",duration:Io})}\n\n      ::view-transition-group(sidebar) {\n          /* Chrome adds a slide-in-out animation by default which looks really bad\n           * because it makes the sidebar fly out of the frame in a weird way.\n           **/\n          animation-name: none;\n      }\n\n      ::view-transition-old(sidebar) {\n          width: unset;\n      }\n\n      #topbar-root {\n        view-transition-name: topbar;\n      }\n      ${t("topbar",{newAnimation:"fade-in",oldAnimation:"fade-out-with-delay",duration:Io})}\n\n\n    `,document.head.appendChild(e),window.addEventListener("click",(e=>{var t,n,r;const o=document.getElementById("sidebar-root"),a=document.getElementById("topbar-root");if(!(e.target instanceof HTMLElement))return;if(!(null==o?void 0:o.contains(e.target))&&!(null==a?void 0:a.contains(e.target)))return;let i;if(e.target instanceof HTMLAnchorElement)i=e.target;else{if(!(e.target instanceof HTMLElement&&e.target.parentElement instanceof HTMLAnchorElement))return;i=e.target.parentElement}const l=Array.from(null!==(t=null==o?void 0:o.querySelectorAll("a"))&&void 0!==t?t:[]),u=Array.from(null!==(r=null===(n=document.getElementById("topbar-root"))||void 0===n?void 0:n.querySelectorAll("a"))&&void 0!==r?r:[]);for(const e of[...l,...u])e!==i?delete e.dataset.active:e.dataset.active="true"})),window.addEventListener("user-sign-out",(()=>{e.remove()}))}());const jo=mo&&"true"===localStorage.getItem("SPA_MODE");function Ro(e,{appId:t,pageId:n,workspaceId:r,paymentPlan:o}){e.setAttribute("data-appid",t),e.setAttribute("data-pageid",n),e.setAttribute("data-workspaceid",r),e.setAttribute("data-paymentplan",o)}const Fo="spr-content-root",zo=`\n    /*\n     * The divider field in v4 blocks has a 100% height within a flex container\n     * causing it to expand in a weird way.\n     * Try removing this rule and add a v4 list block ("List with horizontal cards")\n     * to replicate the issue.\n     *\n     * The issue will be fixed on the blocks side as well, but that won't fix\n     * the older versions of the blocks that are already published.\n     * This is why, this hack is necessary.\n     */\n    .${Fo} div[data-field-type="divider"] > div.list-field-element {\n        height: unset !important;\n    }\n\n    /**\n     * Disable sticky header for v2/v1 header blocks\n     */\n    .${Fo} nav[data-block-id] {\n        position: relative !important;\n    }\n\n    /**\n     * Disable sticky header for v1.0.0 header.\n     *\n     * "[data-block-id]" does not exist for this version.\n     */\n    .${Fo} nav[data-studio-block-id] {\n        position: relative !important;\n    }\n\n    /**\n     * Hack for v3 gallery blocks with an auto sliding gallery\n     * The selector below selects .slick-slider elements inside\n     * an element with a data-block attribute that starts with "gallery"\n     * and contains "v3" in it.\n     */\n    .${Fo} [data-block^="gallery"][data-block*="v3"] .slick-slider\n    {\n        height: max-content;\n    }\n\n    /**\n     * Hack for v1 and v2 Simple text block having extra space at the bottom\n     */\n    .${Fo} [data-block^="text1"] .ql-editor {\n        height: unset !important;\n    }\n\n    /**\n     * Hack for v1 testimonial block\n     */\n    .${Fo} .swiper-wrapper {\n        height: unset !important;\n    }\n\n    .${Fo} [data-block^="video2-v3"] {\n        max-height: 100vh;\n    }\n\n    .${Fo} [data-block^="image2-v3"] {\n        max-height: 100vh;\n    }\n\n    .${Fo} [data-block^="image2-v3"] img.sw-height-full {\n        height: initial !important;\n    }\n\n    /**\n     * Prevent the page from becoming horizontally scrollable when\n     * there are elements that try to take up the full width of the page.\n     * To replicate the issue, add a hero block (any version with a tile\n     * section would do).\n     * Add a word long enough to overflow the screen width e.g. aaaaa....aaaaa\n     * and you'll see that the page becomes horizontally scrollable.\n     */\n    html {\n        overflow-x: hidden;\n    }\n`,Ao=`\n    /**\n     * Make sure each block is selectable in the studio, even if it has\n     * no content.\n     */\n    .${Fo} > [data-block] {\n        min-height: 90px;\n    }\n\n    /**\n     * Headers are typically smaller than 90px so we don't want to\n     * force them to expand.\n     */\n    .${Fo} > [data-block^="header"] {\n        min-height: initial;\n    }\n`,Mo=(0,T.createContext)(null),Bo=(0,T.createContext)(null),$o={render:function(e){return Oo(this,arguments,void 0,(function*({blocks:e,appId:t,pageId:n,workspaceId:r,paymentPlan:a,targetElement:i,baseUrl:l,theme:u,studioEventEmitter:s,appCustomHeaderCode:c,pageCustomHeaderCode:d,appCustomFooterCode:f,pageCustomFooterCode:p,mode:h,blockIdAttributeName:m,slotIdAttributeName:v,slotBlockIdAttributeName:g,featureFlags:y,context:b,spaMode:w=jo}){const k=Object.assign(Object.assign({},Uo),y);w&&(k.fixedBlockRoots=!0),function(e){const t=Math.abs(function(e){const t=JSON.stringify(e);let n,r,o=0;if(0===t.length)return o;for(n=0;n<t.length;n++)r=t.charCodeAt(n),o=(o<<5)-o+r,o|=0;return o}(e))%100,n=new URLSearchParams(location.search),r=[];Object.entries(co).forEach((([e,o])=>{n.has(e)?fo[e]="true"===n.get(e):fo[e]=!!po||o>0&&t<o,fo[e]&&r.push(e)})),r.length&&U.info(`Experimental features enabled for this project: ${r.join(", ")}`)}(t);let x=null;function S(e){x=e}const E=new io;E.push((()=>i.innerHTML=""));const _="studio"!==h,C=document.createElement("div");C.setAttribute("data-spr-role","react-root"),i.setAttribute("data-spr-role","content-root");let j=i;if(!k.fixedBlockRoots&&k.navigationLayout){const e=document.createElement("div");e.id="topbar-root",e.classList.add(P.HORIZONTAL_HEADER_CLASS_NAME);const t=document.createElement("div");t.id="sidebar-root",j=document.createElement("main"),j.id="main-content",j.dataset.instudio=L().toString();const n=document.createElement("div");n.id="bottombar-root",i.appendChild(e),i.appendChild(t),i.appendChild(j),i.appendChild(n)}k.fixedBlockRoots||(document.body.append(C),E.push((()=>C.remove())));const R=k.fixedBlockRoots?(0,N.s)(i):(0,N.s)(C);E.push((()=>R.unmount()));const F=function(e){const t=JSON.parse(JSON.stringify(e.definitions));let n=qn(t,"id"),r={definitions:t,theme:JSON.parse(JSON.stringify(e.theme))};return{update(e){const t=JSON.stringify(e.theme),o=e.definitions.map((e=>{return t=e,r=n[e.id],(t&&r?(t=Object.assign({},t),r=Object.assign({},r),delete t.updated,delete r.updated,JSON.stringify(t)===JSON.stringify(r)):t===r)?n[e.id]:JSON.parse(JSON.stringify(e));var t,r}));return n=qn(o,"id"),r={definitions:o,theme:t===JSON.stringify(r.theme)?r.theme:JSON.parse(t)},r},currentState:()=>r}}({definitions:e,theme:u}),z={appId:t,baseUrl:l,blockIdAttributeName:m,slotBlockIdAttributeName:g,slotIdAttributeName:v,mode:h,spaMode:w,featureFlags:k,lifecycle:E,studioEventEmitter:s};return yield function(e,t){return n=this,r=void 0,a=function*(){const n=uo(e,window),r=uo(e,document);try{yield t()}catch(e){console.error("Error executing script",e)}finally{r(),n()}},new((o=void 0)||(o=Promise))((function(e,t){function i(e){try{u(a.next(e))}catch(e){t(e)}}function l(e){try{u(a.throw(e))}catch(e){t(e)}}function u(t){var n;t.done?e(t.value):(n=t.value,n instanceof o?n:new o((function(e){e(n)}))).then(i,l)}u((a=a.apply(n,r||[])).next())}));var n,r,o,a}(E,(()=>function(e){return Oo(this,void 0,void 0,(function*(){!function(){const e=document.createElement("style");e.setAttribute("data-spr-role","head-styles-sentinel"),document.head.appendChild(e)}();const{definitions:l,theme:u}=F.currentState();i.classList.add(Fo),E.push((()=>i.classList.remove(Fo))),function(e,{theme:t,mode:n,featureFlags:r}){const o=document.createComment("Stick footer to the bottom when the page content is smaller than the viewport");document.head.appendChild(o),e.push((()=>o.remove())),e.push((()=>a.remove()));const a=document.createElement("style");let i=`\n        .${Fo} {\n            background-color: ${t.defaultBackgroundColor};\n        }\n\n        .${Fo} .spr-hidden {\n            display: none;\n        }\n        \n        #page-content:has(* [data-comments-drawer="true"]) {\n            margin-right: 400px;\n            transition: margin-right 0.3s;\n        }\n        \n        #page-content:not(:has(* [data-comments-drawer="true"])) {\n            margin-right: "initial";\n            transition: margin-right 0.3s;\n        }\n    `;"studio"===n||ho||r.navigationLayout||(i+=`\n            .${Fo} {\n                display: flex;\n                flex-direction: column;\n                min-height: 100vh;\n                min-height: 100dvh;\n            }\n\n            /**\n             * Blocks have a data-block="blockname-vx-y-z" attribute,\n             * so the following will select all the footer blocks.\n             */\n            .${Fo} > [data-block^="footer"] {\n                margin-top: auto;\n            }\n\n            .${Fo} > .dynamic-height {\n                flex: 1;\n                display: flex;\n                flex-direction: column;\n            }\n\n            /**\n             * Hack for v2 header block\n             *\n             * It always has #vertical-holder and #data-content-vertical that\n             * should be displayed in a flex row\n             */\n            .${Fo}:has(#vertical-holder) {\n                flex-direction: row;\n            }\n\n            /**\n             * Hack for fixed header + scroll to block:\n             * Fixed header should set --sticky-nav-height css variable, so scroll to block position correctly\n             */\n            .${Fo} [data-block] {\n                scroll-margin-top: var(--sticky-nav-height, 0px);\n            }\n        `),"studio"===n&&(i+=zo,i+=Ao),r.navigationLayout&&(i+=`\n            .${Fo} {\n                display: grid;\n                grid-template-areas: "topbar    topbar"\n                                     "sidebar   main"\n                                     "bottombar bottombar";\n                grid-template-columns: auto minmax(0, 1fr);\n            }\n\n            #topbar-root {\n                grid-area: topbar;\n            }\n\n            /**\n             * When topbar does not exist, make sure page does not get clipped when opened from homescreen.\n             */\n            #topbar-root:empty::before {\n                content: '';\n                display: block;\n                padding-top: env(safe-area-inset-top);\n            }\n\n            #sidebar-root {\n                grid-area: sidebar;\n            }\n\n            #bottombar-root {\n                grid-area: bottombar;\n            }\n\n            #main-content {\n                grid-area: main;\n                display: flex;\n                flex-direction: column;\n                min-height: 100vh;\n                min-height: 100dvh;\n            }\n\n            /**\n             * Tab container buttons in the Studio UI has a special z-index values to be accessible.\n             * However, we don't want them to be on top of the elements outsize the main-content (e.g. sticky header).\n             */\n            #main-content[data-instudio=true] {\n                z-index: 1;\n            }\n\n            /**\n             * Blocks have a data-block="blockname-vx-y-z" attribute,\n             * so the following will select all the footer blocks.\n             */\n            #main-content > [data-block^="footer"] {\n                margin-top: auto;\n            }\n\n            #main-content > .dynamic-height {\n                flex: 1;\n                display: flex;\n                flex-direction: column;\n            }\n\n            /**\n             * Hack for sticky top nav + scroll to block:\n             * Sticky top nav should set --sticky-nav-height css variable, so scroll to block position correctly\n             */\n            #main-content [data-block] {\n                scroll-margin-top: var(--sticky-nav-height, 0px);\n            }\n\n            /**\n             * Hack for progressier widget to not overlap with bottombar\n             */\n             :has(#bottombar-root:not(:empty)) .progressier-widget .progressier-widget-icon {\n                bottom: calc(env(safe-area-inset-bottom) + 58px);\n             }\n\n            /**\n             * Having any different overflow value would lead to elements\n             * with "position: sticky" becoming hidden when page is scrolled\n             * and a modal is open. This does undo a hack for v4 and older blocks.\n             * If you do find an element misbehaving, rather fix it where the problem\n             * occurs instead of here.\n             */\n            html {\n                overflow: visible;\n            }\n        `,"studio"===n&&(i+="\n                /**\n                 * Make sure each block is selectable in the studio, even if it has\n                 * no content.\n                 */\n                #main-content [data-block]:not(.spr-navigation-placeholder) {\n                    min-height: 90px;\n                }\n            ")),ho&&(i+="\n            body, #main-content {\n                min-height: initial;\n            }\n        "),a.innerHTML=i,document.head.appendChild(a)}(E,{theme:u,mode:h,featureFlags:k}),c&&_&&(yield ao({at:document.head,code:c,debugLabel:"appHeader",lifecycle:E})),d&&_&&(yield ao({at:document.head,code:d,debugLabel:"pageHeader",lifecycle:E})),Ro(i,{appId:t,pageId:n,workspaceId:r,paymentPlan:a}),k.fixedBlockRoots||Y(j,l);const s=(0,o.jsx)(T.StrictMode,{children:(0,o.jsx)(gr.Provider,{value:z,children:(0,o.jsx)(Bo.Provider,{value:b.application_context,children:(0,o.jsx)(Zr,{ref:S,definitions:l,theme:u,pageId:n})})})});(0,T.startTransition)((()=>{_?(0,I.flushSync)((()=>e.render(s))):e.render(s)})),!k.fixedBlockRoots&&_&&(yield function(e){return Oo(this,void 0,void 0,(function*(){const t=J(e);for(const n of e){if(t[n.id])continue;if(!O(n))continue;if(!Cr(n))continue;const[e,r]=ae(n);for(const t of D(e,r))t instanceof HTMLElement&&(yield Pr(t))}}))}(l)),f&&_&&(yield ao({at:i,code:f,debugLabel:"appFooter",lifecycle:E})),p&&_&&(yield ao({at:i,code:p,debugLabel:"pageFooter",lifecycle:E})),_&&(yield function(e,t,n){return Oo(this,void 0,void 0,(function*(){const n=new AbortController;var r;yield Promise.race([Do(e,t,n.signal),(r=5e3,new Promise((e=>setTimeout(e,r)))).then((()=>n.abort()))])}))}(E,l),document.dispatchEvent(new CustomEvent(le,{bubbles:!0})),document.dispatchEvent(new CustomEvent(ue,{bubbles:!1,cancelable:!1})),window.dispatchEvent(new CustomEvent(ue,{bubbles:!1,cancelable:!1})))}))}(R))),{update:function({blocks:e,theme:t,pageId:n,appId:l,context:u}){U.debug("SoftrPageRenderer.update",{blocks:e,theme:t,pageId:n,appId:l});const s=!k.fixedBlockRoots&&function(e,t){const n=new Set(G(t).map((e=>e.id)));for(const t of q(e)){const e=t.getAttribute("data-spr-block-id");if(!e)throw new Error("data-spr-block-id not found in block root. This is likely to be a bug");if(!n.has(e))return!0}for(const[t]of oe(e))if(!n.has(t))return!0;return!1}(i,e),{definitions:c,theme:d}=F.update({definitions:e,theme:t});Ro(i,{appId:l,pageId:n,workspaceId:r,paymentPlan:a}),k.fixedBlockRoots||Y(j,c),(0,T.startTransition)((()=>{(e=>{e=(0,o.jsx)(T.StrictMode,{children:e}),s?(U.info("Block roots/sentinels need cleanup. Re-rendering the page synchronously."),(0,I.flushSync)((()=>R.render(e)))):R.render(e)})((0,o.jsx)(gr.Provider,{value:z,children:(0,o.jsx)(Bo.Provider,{value:u.application_context,children:(0,o.jsx)(Zr,{ref:S,definitions:c,theme:d,pageId:n})})}))})),s&&function(e,t){const n=new Set(G(t).map((e=>e.id)));for(const t of q(e)){const e=t.getAttribute("data-spr-block-id");if(!e)throw new Error("data-spr-block-id not found in block root. This is likely to be a bug");n.has(e)||(U.debug("Removing block root for",t.getAttribute(ie)),t.remove())}for(const[t,r,o]of oe(e))if(!n.has(t)){U.debug("Removing block root for",r.getAttribute(ie));for(const t of D(r,o))e.removeChild(t)}}(j,c)},getBlockById:function(e){if(!x)throw new Error("Page ref is not ready.");return x.blockInstanceMap.get(e)||null},dispose:()=>{U.info("Disposing the page renderer"),R.unmount(),E.dispose()}}}))},ChildBlock:function({definition:e,wrapperProps:t}){var n,r,a;const{featureFlags:{fixedBlockRoots:i}}=yr(),{nestedBlocks:l}=sr();if(i)return(0,o.jsx)(Wr,{definition:e,childBlocks:null!==(n=l[e.id])&&void 0!==n?n:so,topLevel:!1,childBlockRootProps:t});const u={"data-block":Q(e),[fr]:e.id,[hr]:e.id};return(0,o.jsx)("div",Object.assign({id:e.hrid},u,t,{className:`block-${e.id} ${null!==(r=null==t?void 0:t.className)&&void 0!==r?r:""}`,children:(0,o.jsx)(Wr,{definition:e,childBlocks:null!==(a=l[e.id])&&void 0!==a?a:so,topLevel:!1,childBlockRootProps:null})}))},BlockContainerSlotContext:Mo,AppContext:Bo};function Do(e,t,n){const r=t.filter((e=>!O(e))),o=new Set;return r.every((e=>o.has(e.id)))?Promise.resolve():new Promise(((t,a)=>{const{dispose:i}=ce((e=>{if(n.aborted)return a("ABORTED");o.add(e.detail.blockId),r.every((e=>o.has(e.id)))&&(t(),i())}));e.push((()=>i())),n.addEventListener("abort",(()=>i()))}))}const Uo={navigationLayout:!1,fixedBlockRoots:mo},Ho=$o})(),self.SoftrPageRenderer=r.default})();